import { BaseFacilityDto, BaseFacilityResponseDto, BasePaginatedResponseDto, BaseCreateResponseDto, BaseUpdateResponseDto } from './base-facility.dto';
export declare class CreateHouseholdDto extends BaseFacilityDto {
}
declare const UpdateHouseholdDto_base: import("@nestjs/common").Type<Partial<CreateHouseholdDto>>;
export declare class UpdateHouseholdDto extends UpdateHouseholdDto_base {
}
export declare class HouseholdResponseDto extends BaseFacilityResponseDto {
}
export declare class HouseholdsListResponseDto extends BasePaginatedResponseDto<HouseholdResponseDto> {
    data: HouseholdResponseDto[];
}
export declare class CreateHouseholdResponseDto extends BaseCreateResponseDto<HouseholdResponseDto> {
    facility: HouseholdResponseDto;
    message: string;
}
export declare class UpdateHouseholdResponseDto extends BaseUpdateResponseDto<HouseholdResponseDto> {
    facility: HouseholdResponseDto;
    message: string;
}
export {};
