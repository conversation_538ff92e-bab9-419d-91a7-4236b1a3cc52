import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateSectorDto } from './create-sector.dto';

export class UpdateSectorDto extends PartialType(CreateSectorDto) {}

export class UpdateSectorResponseDto {
  @ApiProperty({
    description: 'Updated sector information',
  })
  sector: {
    id: number;
    code: number;
    name: string;
    districtId: number;
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Sector updated successfully',
  })
  message: string;
}
