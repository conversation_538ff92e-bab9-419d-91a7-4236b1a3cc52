"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacilitiesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let FacilitiesService = class FacilitiesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createHousehold(createHouseholdDto) {
        const existingHousehold = await this.prisma.houseHold.findUnique({
            where: { number: createHouseholdDto.number },
        });
        if (existingHousehold) {
            throw new common_1.ConflictException('Household number already exists');
        }
        const location = await this.prisma.location.findUnique({
            where: { id: createHouseholdDto.locationId },
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        const household = await this.prisma.houseHold.create({
            data: {
                locationId: createHouseholdDto.locationId,
                number: createHouseholdDto.number,
            },
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            facility: this.formatHouseholdResponse(household),
            message: 'Household created successfully',
        };
    }
    async findAllHouseholds(page = 1, limit = 10, search) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(search && {
                OR: [
                    { number: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                ].filter(Boolean),
            }),
        };
        const [households, total] = await Promise.all([
            this.prisma.houseHold.findMany({
                where,
                skip,
                take: limit,
                include: {
                    location: {
                        include: {
                            village: {
                                include: {
                                    cell: {
                                        include: {
                                            sector: {
                                                include: {
                                                    district: {
                                                        include: {
                                                            province: true,
                                                        },
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    number: 'asc',
                },
            }),
            this.prisma.houseHold.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            data: households.map((household) => this.formatHouseholdResponse(household)),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOneHousehold(id) {
        const household = await this.prisma.houseHold.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!household) {
            throw new common_1.NotFoundException('Household not found');
        }
        return this.formatHouseholdResponse(household);
    }
    async updateHousehold(id, updateHouseholdDto) {
        const existingHousehold = await this.prisma.houseHold.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingHousehold) {
            throw new common_1.NotFoundException('Household not found');
        }
        if (updateHouseholdDto.number && updateHouseholdDto.number !== existingHousehold.number) {
            const conflictingHousehold = await this.prisma.houseHold.findUnique({
                where: { number: updateHouseholdDto.number },
            });
            if (conflictingHousehold) {
                throw new common_1.ConflictException('Household number already exists');
            }
        }
        if (updateHouseholdDto.locationId) {
            const location = await this.prisma.location.findUnique({
                where: { id: updateHouseholdDto.locationId },
            });
            if (!location) {
                throw new common_1.NotFoundException('Location not found');
            }
        }
        const household = await this.prisma.houseHold.update({
            where: { id },
            data: updateHouseholdDto,
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            facility: this.formatHouseholdResponse(household),
            message: 'Household updated successfully',
        };
    }
    async removeHousehold(id) {
        const household = await this.prisma.houseHold.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!household) {
            throw new common_1.NotFoundException('Household not found');
        }
        await this.prisma.houseHold.update({
            where: { id },
            data: { deleted: true },
        });
        return {
            message: 'Household deleted successfully',
        };
    }
    formatHouseholdResponse(household) {
        return {
            id: household.id,
            number: household.number,
            location: {
                id: household.location.id,
                villageId: household.location.villageId,
                village: {
                    id: household.location.village.id,
                    name: household.location.village.name,
                    cell: {
                        id: household.location.village.cell.id,
                        name: household.location.village.cell.name,
                        sector: {
                            id: household.location.village.cell.sector.id,
                            name: household.location.village.cell.sector.name,
                            district: {
                                id: household.location.village.cell.sector.district.id,
                                name: household.location.village.cell.sector.district.name,
                                province: {
                                    id: household.location.village.cell.sector.district.province.id,
                                    name: household.location.village.cell.sector.district.province.name,
                                },
                            },
                        },
                    },
                },
                latitude: household.location.latitude,
                longitude: household.location.longitude,
                settlementType: household.location.settlementType,
            },
            createdAt: household.createdAt,
            updatedAt: household.updatedAt,
        };
    }
    async createSchool(createSchoolDto) {
        const existingSchool = await this.prisma.school.findUnique({
            where: { number: createSchoolDto.number },
        });
        if (existingSchool) {
            throw new common_1.ConflictException('School number already exists');
        }
        const location = await this.prisma.location.findUnique({
            where: { id: createSchoolDto.locationId },
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        const school = await this.prisma.school.create({
            data: {
                locationId: createSchoolDto.locationId,
                number: createSchoolDto.number,
                name: createSchoolDto.name,
            },
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            facility: this.formatSchoolResponse(school),
            message: 'School created successfully',
        };
    }
    async findAllSchools(page = 1, limit = 10, search) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(search && {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { number: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                ].filter(Boolean),
            }),
        };
        const [schools, total] = await Promise.all([
            this.prisma.school.findMany({
                where,
                skip,
                take: limit,
                include: {
                    location: {
                        include: {
                            village: {
                                include: {
                                    cell: {
                                        include: {
                                            sector: {
                                                include: {
                                                    district: {
                                                        include: {
                                                            province: true,
                                                        },
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    number: 'asc',
                },
            }),
            this.prisma.school.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            data: schools.map((school) => this.formatSchoolResponse(school)),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOneSchool(id) {
        const school = await this.prisma.school.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!school) {
            throw new common_1.NotFoundException('School not found');
        }
        return this.formatSchoolResponse(school);
    }
    async updateSchool(id, updateSchoolDto) {
        const existingSchool = await this.prisma.school.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingSchool) {
            throw new common_1.NotFoundException('School not found');
        }
        if (updateSchoolDto.number && updateSchoolDto.number !== existingSchool.number) {
            const conflictingSchool = await this.prisma.school.findUnique({
                where: { number: updateSchoolDto.number },
            });
            if (conflictingSchool) {
                throw new common_1.ConflictException('School number already exists');
            }
        }
        if (updateSchoolDto.locationId) {
            const location = await this.prisma.location.findUnique({
                where: { id: updateSchoolDto.locationId },
            });
            if (!location) {
                throw new common_1.NotFoundException('Location not found');
            }
        }
        const school = await this.prisma.school.update({
            where: { id },
            data: updateSchoolDto,
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            facility: this.formatSchoolResponse(school),
            message: 'School updated successfully',
        };
    }
    async removeSchool(id) {
        const school = await this.prisma.school.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!school) {
            throw new common_1.NotFoundException('School not found');
        }
        await this.prisma.school.update({
            where: { id },
            data: { deleted: true },
        });
        return {
            message: 'School deleted successfully',
        };
    }
    formatSchoolResponse(school) {
        return {
            id: school.id,
            number: school.number,
            name: school.name,
            location: {
                id: school.location.id,
                villageId: school.location.villageId,
                village: {
                    id: school.location.village.id,
                    name: school.location.village.name,
                    cell: {
                        id: school.location.village.cell.id,
                        name: school.location.village.cell.name,
                        sector: {
                            id: school.location.village.cell.sector.id,
                            name: school.location.village.cell.sector.name,
                            district: {
                                id: school.location.village.cell.sector.district.id,
                                name: school.location.village.cell.sector.district.name,
                                province: {
                                    id: school.location.village.cell.sector.district.province.id,
                                    name: school.location.village.cell.sector.district.province.name,
                                },
                            },
                        },
                    },
                },
                latitude: school.location.latitude,
                longitude: school.location.longitude,
                settlementType: school.location.settlementType,
            },
            createdAt: school.createdAt,
            updatedAt: school.updatedAt,
        };
    }
    async createHealthFacility(createHealthFacilityDto) {
        const existingHealthFacility = await this.prisma.healthFacility.findUnique({
            where: { number: createHealthFacilityDto.number },
        });
        if (existingHealthFacility) {
            throw new common_1.ConflictException('Health facility number already exists');
        }
        const location = await this.prisma.location.findUnique({
            where: { id: createHealthFacilityDto.locationId },
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        const healthFacility = await this.prisma.healthFacility.create({
            data: {
                locationId: createHealthFacilityDto.locationId,
                number: createHealthFacilityDto.number,
                name: createHealthFacilityDto.name,
            },
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            facility: this.formatHealthFacilityResponse(healthFacility),
            message: 'Health facility created successfully',
        };
    }
    async findAllHealthFacilities(page = 1, limit = 10, search) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(search && {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { number: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                ].filter(Boolean),
            }),
        };
        const [healthFacilities, total] = await Promise.all([
            this.prisma.healthFacility.findMany({
                where,
                skip,
                take: limit,
                include: {
                    location: {
                        include: {
                            village: {
                                include: {
                                    cell: {
                                        include: {
                                            sector: {
                                                include: {
                                                    district: {
                                                        include: {
                                                            province: true,
                                                        },
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    number: 'asc',
                },
            }),
            this.prisma.healthFacility.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            data: healthFacilities.map((healthFacility) => this.formatHealthFacilityResponse(healthFacility)),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOneHealthFacility(id) {
        const healthFacility = await this.prisma.healthFacility.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!healthFacility) {
            throw new common_1.NotFoundException('Health facility not found');
        }
        return this.formatHealthFacilityResponse(healthFacility);
    }
    async updateHealthFacility(id, updateHealthFacilityDto) {
        const existingHealthFacility = await this.prisma.healthFacility.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingHealthFacility) {
            throw new common_1.NotFoundException('Health facility not found');
        }
        if (updateHealthFacilityDto.number && updateHealthFacilityDto.number !== existingHealthFacility.number) {
            const conflictingHealthFacility = await this.prisma.healthFacility.findUnique({
                where: { number: updateHealthFacilityDto.number },
            });
            if (conflictingHealthFacility) {
                throw new common_1.ConflictException('Health facility number already exists');
            }
        }
        if (updateHealthFacilityDto.locationId) {
            const location = await this.prisma.location.findUnique({
                where: { id: updateHealthFacilityDto.locationId },
            });
            if (!location) {
                throw new common_1.NotFoundException('Location not found');
            }
        }
        const healthFacility = await this.prisma.healthFacility.update({
            where: { id },
            data: updateHealthFacilityDto,
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            facility: this.formatHealthFacilityResponse(healthFacility),
            message: 'Health facility updated successfully',
        };
    }
    async removeHealthFacility(id) {
        const healthFacility = await this.prisma.healthFacility.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!healthFacility) {
            throw new common_1.NotFoundException('Health facility not found');
        }
        await this.prisma.healthFacility.update({
            where: { id },
            data: { deleted: true },
        });
        return {
            message: 'Health facility deleted successfully',
        };
    }
    formatHealthFacilityResponse(healthFacility) {
        return {
            id: healthFacility.id,
            number: healthFacility.number,
            name: healthFacility.name,
            location: {
                id: healthFacility.location.id,
                villageId: healthFacility.location.villageId,
                village: {
                    id: healthFacility.location.village.id,
                    name: healthFacility.location.village.name,
                    cell: {
                        id: healthFacility.location.village.cell.id,
                        name: healthFacility.location.village.cell.name,
                        sector: {
                            id: healthFacility.location.village.cell.sector.id,
                            name: healthFacility.location.village.cell.sector.name,
                            district: {
                                id: healthFacility.location.village.cell.sector.district.id,
                                name: healthFacility.location.village.cell.sector.district.name,
                                province: {
                                    id: healthFacility.location.village.cell.sector.district.province.id,
                                    name: healthFacility.location.village.cell.sector.district.province.name,
                                },
                            },
                        },
                    },
                },
                latitude: healthFacility.location.latitude,
                longitude: healthFacility.location.longitude,
                settlementType: healthFacility.location.settlementType,
            },
            createdAt: healthFacility.createdAt,
            updatedAt: healthFacility.updatedAt,
        };
    }
    async createMarket(createMarketDto) {
        const existingMarket = await this.prisma.market.findUnique({
            where: { number: createMarketDto.number },
        });
        if (existingMarket) {
            throw new common_1.ConflictException('Market number already exists');
        }
        const location = await this.prisma.location.findUnique({
            where: { id: createMarketDto.locationId },
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        const market = await this.prisma.market.create({
            data: {
                locationId: createMarketDto.locationId,
                number: createMarketDto.number,
                name: createMarketDto.name,
            },
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            facility: this.formatMarketResponse(market),
            message: 'Market created successfully',
        };
    }
    async findAllMarkets(page = 1, limit = 10, search) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(search && {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { number: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                ].filter(Boolean),
            }),
        };
        const [markets, total] = await Promise.all([
            this.prisma.market.findMany({
                where,
                skip,
                take: limit,
                include: {
                    location: {
                        include: {
                            village: {
                                include: {
                                    cell: {
                                        include: {
                                            sector: {
                                                include: {
                                                    district: {
                                                        include: {
                                                            province: true,
                                                        },
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    number: 'asc',
                },
            }),
            this.prisma.market.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            data: markets.map((market) => this.formatMarketResponse(market)),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOneMarket(id) {
        const market = await this.prisma.market.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!market) {
            throw new common_1.NotFoundException('Market not found');
        }
        return this.formatMarketResponse(market);
    }
    async updateMarket(id, updateMarketDto) {
        const existingMarket = await this.prisma.market.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingMarket) {
            throw new common_1.NotFoundException('Market not found');
        }
        if (updateMarketDto.number && updateMarketDto.number !== existingMarket.number) {
            const conflictingMarket = await this.prisma.market.findUnique({
                where: { number: updateMarketDto.number },
            });
            if (conflictingMarket) {
                throw new common_1.ConflictException('Market number already exists');
            }
        }
        if (updateMarketDto.locationId) {
            const location = await this.prisma.location.findUnique({
                where: { id: updateMarketDto.locationId },
            });
            if (!location) {
                throw new common_1.NotFoundException('Location not found');
            }
        }
        const market = await this.prisma.market.update({
            where: { id },
            data: updateMarketDto,
            include: {
                location: {
                    include: {
                        village: {
                            include: {
                                cell: {
                                    include: {
                                        sector: {
                                            include: {
                                                district: {
                                                    include: {
                                                        province: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            facility: this.formatMarketResponse(market),
            message: 'Market updated successfully',
        };
    }
    async removeMarket(id) {
        const market = await this.prisma.market.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!market) {
            throw new common_1.NotFoundException('Market not found');
        }
        await this.prisma.market.update({
            where: { id },
            data: { deleted: true },
        });
        return {
            message: 'Market deleted successfully',
        };
    }
    formatMarketResponse(market) {
        return {
            id: market.id,
            number: market.number,
            name: market.name,
            location: {
                id: market.location.id,
                villageId: market.location.villageId,
                village: {
                    id: market.location.village.id,
                    name: market.location.village.name,
                    cell: {
                        id: market.location.village.cell.id,
                        name: market.location.village.cell.name,
                        sector: {
                            id: market.location.village.cell.sector.id,
                            name: market.location.village.cell.sector.name,
                            district: {
                                id: market.location.village.cell.sector.district.id,
                                name: market.location.village.cell.sector.district.name,
                                province: {
                                    id: market.location.village.cell.sector.district.province.id,
                                    name: market.location.village.cell.sector.district.province.name,
                                },
                            },
                        },
                    },
                },
                latitude: market.location.latitude,
                longitude: market.location.longitude,
                settlementType: market.location.settlementType,
            },
            createdAt: market.createdAt,
            updatedAt: market.updatedAt,
        };
    }
};
exports.FacilitiesService = FacilitiesService;
exports.FacilitiesService = FacilitiesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FacilitiesService);
//# sourceMappingURL=facilities.service.js.map