# Prometheus configuration for WASH MIS monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'wash-mis'
    environment: 'development'

# Remote write to Mimi<PERSON>
remote_write:
  - url: http://mimir:9009/api/v1/push
    headers:
        X-Scope-OrgID: "wash-mis"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Rule files
rule_files:
  - "/etc/prometheus/rules/*.yml"

# Scrape configurations
scrape_configs:
  # WASH MIS NestJS Application
  - job_name: 'wash-mis-api'
    static_configs:
      - targets: ['host.docker.internal:8080']
    metrics_path: '/api/v1/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s

  # Mimir monitoring
  - job_name: 'mimir'
    static_configs:
      - targets: ['mimir:9009']
    scrape_interval: 15s

  # Loki monitoring
  - job_name: 'loki'
    static_configs:
      - targets: ['loki:3100']
    scrape_interval: 15s

  # Tempo monitoring
  - job_name: 'tempo'
    static_configs:
      - targets: ['tempo:3200']
    scrape_interval: 15s

  # Grafana monitoring
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 15s

  # PostgreSQL monitoring (if postgres_exporter is available)
  - job_name: 'postgres'
    static_configs:
      - targets: ['host.docker.internal:9187']
    scrape_interval: 30s
    scrape_timeout: 10s
