"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCellResponseDto = exports.UpdateCellDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_cell_dto_1 = require("./create-cell.dto");
class UpdateCellDto extends (0, swagger_1.PartialType)(create_cell_dto_1.CreateCellDto) {
}
exports.UpdateCellDto = UpdateCellDto;
class UpdateCellResponseDto {
    cell;
    message;
}
exports.UpdateCellResponseDto = UpdateCellResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated cell information',
    }),
    __metadata("design:type", Object)
], UpdateCellResponseDto.prototype, "cell", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Cell updated successfully',
    }),
    __metadata("design:type", String)
], UpdateCellResponseDto.prototype, "message", void 0);
//# sourceMappingURL=update-cell.dto.js.map