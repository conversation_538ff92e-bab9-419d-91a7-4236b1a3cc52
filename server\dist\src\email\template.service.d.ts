import { ConfigService } from '@nestjs/config';
export interface TemplateContext {
    [key: string]: any;
}
export declare class TemplateService {
    private readonly configService;
    private readonly logger;
    private readonly templatesDir;
    private readonly compiledTemplates;
    constructor(configService: ConfigService);
    private registerHelpers;
    private getTemplate;
    private getBaseContext;
    renderTemplate(templateName: string, context: TemplateContext): Promise<string>;
    clearTemplateCache(): void;
}
