"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateDistrictResponseDto = exports.UpdateDistrictDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_district_dto_1 = require("./create-district.dto");
class UpdateDistrictDto extends (0, swagger_1.PartialType)(create_district_dto_1.CreateDistrictDto) {
}
exports.UpdateDistrictDto = UpdateDistrictDto;
class UpdateDistrictResponseDto {
    district;
    message;
}
exports.UpdateDistrictResponseDto = UpdateDistrictResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated district information',
    }),
    __metadata("design:type", Object)
], UpdateDistrictResponseDto.prototype, "district", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'District updated successfully',
    }),
    __metadata("design:type", String)
], UpdateDistrictResponseDto.prototype, "message", void 0);
//# sourceMappingURL=update-district.dto.js.map