import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { CellsService } from './cells.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { PrivilegeGuard } from '../common/guards/privilege.guard';
import { MobileGuard } from '../common/guards/mobile.guard';
import { Privileges } from '../common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';
import {
  CreateCellDto,
  CreateCellResponseDto,
} from './dto/create-cell.dto';
import {
  UpdateCellDto,
  UpdateCellResponseDto,
} from './dto/update-cell.dto';
import {
  CellResponseDto,
  CellsListResponseDto,
} from './dto/cell-response.dto';

@ApiTags('Cells')
@Controller('cells')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
export class CellsController {
  constructor(private readonly cellsService: CellsService) {}

  @Post()
  @Privileges(Privilege.USER_MANAGEMENT)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new cell' })
  @ApiResponse({
    status: 201,
    description: 'Cell created successfully',
    type: CreateCellResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Sector not found' })
  @ApiResponse({ status: 409, description: 'Cell already exists' })
  async create(@Body() createCellDto: CreateCellDto) {
    return this.cellsService.create(createCellDto);
  }

  @Get()
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get all cells with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by cell name or code',
    example: 'Gisozi',
  })
  @ApiQuery({
    name: 'sectorId',
    required: false,
    type: Number,
    description: 'Filter by sector ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Cells retrieved successfully',
    type: CellsListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('search') search?: string,
    @Query('sectorId', new ParseIntPipe({ optional: true })) sectorId?: number,
  ) {
    return this.cellsService.findAll(page, limit, search, sectorId);
  }

  @Get(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get a cell by ID' })
  @ApiParam({
    name: 'id',
    description: 'Cell ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Cell retrieved successfully',
    type: CellResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Cell not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.cellsService.findOne(id);
  }

  @Patch(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Update a cell' })
  @ApiParam({
    name: 'id',
    description: 'Cell ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Cell updated successfully',
    type: UpdateCellResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Cell or Sector not found' })
  @ApiResponse({ status: 409, description: 'Cell already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateCellDto: UpdateCellDto) {
    return this.cellsService.update(id, updateCellDto);
  }

  @Delete(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Delete a cell' })
  @ApiParam({
    name: 'id',
    description: 'Cell ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Cell deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Cell deleted successfully',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - Cell has villages' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Cell not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.cellsService.remove(id);
  }
}
