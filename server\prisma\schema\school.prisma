model School {
    id         String   @id @default(uuid())
    location   Location @relation(fields: [locationId], references: [id])
    locationId String
    name       String
    number     Int      @unique

    deleted Boolean @default(false)


    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    Submission Submission[]
}

model SchoolGeneralInfo {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    schoolName               String
    schoolType               SchoolType
    managementType           SchoolManagement
    dayBoarding              SchoolTypeDayBoarding
    totalStudents            Int
    femaleStudents           Int
    maleStudents             Int
    studentsWithDisabilities Int
}

model SchoolWaterSupply {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    connectedToPipeline Boolean
    waterAvailability   Boolean
    availableDays       WaterAvailabilityFrequency?
    storageCapacity     CleanWaterStorageCapacity
    mainWaterSource     MainWaterSource?
    distanceToSource    WaterSourceDistance
}

model SchoolSanitation {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    toiletType                ToiletFacilityType
    slabConstructionMaterial  FacilitySlabConstructionMaterial
    totalToilets              Int
    genderSeparation          Boolean
    femaleToilets             Int
    maleToilets               Int
    girlsRoom                 Boolean
    disabilityAccess          Boolean
    staffToilets              Boolean
    hasToiletFullInLast2Years Boolean
    excretaManagement         ExcretaManagement?
}

model SchoolHygiene {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    handwashingFacility           Boolean
    facilityType                  HandWashingFacilityType?
    handwashingMaterials          HandWashingMaterial?
    handWashingfacilityNearToilet Boolean?
    toiletHandWashingFacilityType HandWashingFacilityType?
    toiletHandwashingMaterials    HandWashingMaterial?
}

model SchoolSolidWasteManagement {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    wasteSeparation     Boolean
    wasteManagement     WasteManagementAfterSeparation?
    treatmentType       WasteTreatmentType?
    collectionFrequency WasteCollectionFrequency?
    collectionCost      Int?
}

model SchoolLiquidWasteManagement {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    liquidWasteManagement WasteWaterManagement
}
