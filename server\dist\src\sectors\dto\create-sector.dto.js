"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSectorResponseDto = exports.CreateSectorDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateSectorDto {
    code;
    name;
    districtId;
}
exports.CreateSectorDto = CreateSectorDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector code (unique identifier)',
        example: 10101,
        minimum: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateSectorDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector name',
        example: 'Gitega',
        minLength: 2,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    __metadata("design:type", String)
], CreateSectorDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District ID this sector belongs to',
        example: 1,
        minimum: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateSectorDto.prototype, "districtId", void 0);
class CreateSectorResponseDto {
    sector;
    message;
}
exports.CreateSectorResponseDto = CreateSectorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created sector information',
    }),
    __metadata("design:type", Object)
], CreateSectorResponseDto.prototype, "sector", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Sector created successfully',
    }),
    __metadata("design:type", String)
], CreateSectorResponseDto.prototype, "message", void 0);
//# sourceMappingURL=create-sector.dto.js.map