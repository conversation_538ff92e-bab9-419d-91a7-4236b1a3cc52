"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringService = void 0;
const common_1 = require("@nestjs/common");
const metrics_1 = require("../metrics");
const logger_1 = require("../logger");
const api_1 = require("@opentelemetry/api");
let MonitoringService = class MonitoringService {
    logger = (0, logger_1.createModuleLogger)('monitoring');
    recordAuthenticationAttempt(type, status, method = 'local') {
        metrics_1.authenticationAttempts.inc({ type, status, method });
        this.logger.info('Authentication attempt recorded', {
            type,
            status,
            method,
        });
        const span = api_1.trace.getActiveSpan();
        if (span) {
            span.setAttributes({
                'auth.type': type,
                'auth.status': status,
                'auth.method': method,
            });
        }
    }
    updateActiveUserSessions(role, count) {
        metrics_1.activeUserSessions.set({ role }, count);
    }
    recordDatabaseQuery(operation, table, status, durationMs) {
        const durationSeconds = durationMs / 1000;
        metrics_1.databaseQueryDuration.observe({ operation, table, status }, durationSeconds);
        if (durationMs > 1000) {
            this.logger.warn('Slow database query detected', {
                operation,
                table,
                status,
                duration_ms: durationMs,
            });
        }
        const span = api_1.trace.getActiveSpan();
        if (span) {
            span.setAttributes({
                'db.operation': operation,
                'db.table': table,
                'db.status': status,
                'db.duration_ms': durationMs,
            });
        }
    }
    recordDataSubmission(facilityType, locationType, status) {
        metrics_1.dataSubmissions.inc({ facility_type: facilityType, location_type: locationType, status });
        this.logger.info('Data submission recorded', {
            facility_type: facilityType,
            location_type: locationType,
            status,
        });
        const span = api_1.trace.getActiveSpan();
        if (span) {
            span.setAttributes({
                'wash_mis.facility_type': facilityType,
                'wash_mis.location_type': locationType,
                'wash_mis.submission_status': status,
            });
        }
    }
    recordUserRegistration(role, status) {
        metrics_1.userRegistrations.inc({ role, status });
        this.logger.info('User registration recorded', {
            role,
            status,
        });
    }
    updateFacilityCount(type, province, district, count) {
        metrics_1.facilityCount.set({ type, province, district }, count);
    }
    updateLocationCoverage(province, district, sector, metricType, value) {
        metrics_1.locationCoverage.set({ province, district, sector, metric_type: metricType }, value);
    }
    recordEmailSent(type, status) {
        metrics_1.emailsSent.inc({ type, status });
        this.logger.info('Email sent recorded', {
            type,
            status,
        });
    }
    recordApplicationError(type, severity, module, error) {
        metrics_1.applicationErrors.inc({ type, severity, module });
        this.logger.error('Application error recorded', {
            type,
            severity,
            module,
            error_message: error?.message,
            error_stack: error?.stack,
        });
        const span = api_1.trace.getActiveSpan();
        if (span) {
            span.setAttributes({
                'error.type': type,
                'error.severity': severity,
                'error.module': module,
                'error.message': error?.message || 'Unknown error',
            });
            span.recordException(error || new Error('Unknown error'));
        }
    }
    recordCacheOperation(operation, status) {
        metrics_1.cacheOperations.inc({ operation, status });
    }
    createBusinessSpan(operationName, attributes) {
        const tracer = api_1.trace.getTracer('wash-mis-business');
        const span = tracer.startSpan(operationName);
        if (attributes) {
            span.setAttributes(attributes);
        }
        return span;
    }
    async trackBusinessOperation(operationName, operation, attributes) {
        const span = this.createBusinessSpan(operationName, attributes);
        const startTime = Date.now();
        try {
            const result = await operation();
            span.setStatus({ code: 1 });
            return result;
        }
        catch (error) {
            span.setStatus({ code: 2, message: error.message });
            span.recordException(error);
            this.recordApplicationError('business', 'high', operationName, error);
            throw error;
        }
        finally {
            const duration = Date.now() - startTime;
            span.setAttributes({ 'operation.duration_ms': duration });
            span.end();
            this.logger.info('Business operation completed', {
                operation: operationName,
                duration_ms: duration,
                ...attributes,
            });
        }
    }
};
exports.MonitoringService = MonitoringService;
exports.MonitoringService = MonitoringService = __decorate([
    (0, common_1.Injectable)()
], MonitoringService);
//# sourceMappingURL=monitoring.service.js.map