import { SectorsService } from './sectors.service';
import { CreateSectorDto, CreateSectorResponseDto } from './dto/create-sector.dto';
import { UpdateSectorDto, UpdateSectorResponseDto } from './dto/update-sector.dto';
import { SectorResponseDto, SectorsListResponseDto } from './dto/sector-response.dto';
export declare class SectorsController {
    private readonly sectorsService;
    constructor(sectorsService: SectorsService);
    create(createSectorDto: CreateSectorDto): Promise<CreateSectorResponseDto>;
    findAll(page?: number, limit?: number, search?: string, districtId?: number): Promise<SectorsListResponseDto>;
    findOne(id: number): Promise<SectorResponseDto>;
    update(id: number, updateSectorDto: UpdateSectorDto): Promise<UpdateSectorResponseDto>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
