{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "<PERSON>mir"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "<PERSON>mir"}, "expr": "histogram_quantile(0.95, rate(wash_mis_http_request_duration_seconds_bucket[5m]))", "interval": "", "legendFormat": "95th percentile", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "<PERSON>mir"}, "expr": "histogram_quantile(0.50, rate(wash_mis_http_request_duration_seconds_bucket[5m]))", "interval": "", "legendFormat": "50th percentile", "refId": "B"}], "title": "HTTP Request Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "<PERSON>mir"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "<PERSON>mir"}, "expr": "wash_mis_process_resident_memory_bytes", "interval": "", "legendFormat": "RSS Memory", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "<PERSON>mir"}, "expr": "wash_mis_nodejs_heap_size_used_bytes", "interval": "", "legendFormat": "Heap Used", "refId": "B"}], "title": "Memory Usage", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 36, "style": "dark", "tags": ["<PERSON><PERSON><PERSON>", "performance", "application"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "NestJS Application Performance", "uid": "nestjs-performance", "version": 1, "weekStart": ""}