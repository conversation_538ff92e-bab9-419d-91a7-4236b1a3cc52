import { ProvincesService } from './provinces.service';
import { CreateProvinceDto, CreateProvinceResponseDto } from './dto/create-province.dto';
import { UpdateProvinceDto, UpdateProvinceResponseDto } from './dto/update-province.dto';
import { ProvinceResponseDto, ProvincesListResponseDto } from './dto/province-response.dto';
export declare class ProvincesController {
    private readonly provincesService;
    constructor(provincesService: ProvincesService);
    create(createProvinceDto: CreateProvinceDto): Promise<CreateProvinceResponseDto>;
    findAll(page?: number, limit?: number, search?: string): Promise<ProvincesListResponseDto>;
    findOne(id: number): Promise<ProvinceResponseDto>;
    update(id: number, updateProvinceDto: UpdateProvinceDto): Promise<UpdateProvinceResponseDto>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
