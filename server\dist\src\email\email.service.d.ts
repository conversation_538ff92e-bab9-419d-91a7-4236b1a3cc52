import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { TemplateService } from "./template.service";
export declare class EmailService {
    private readonly httpService;
    private readonly configService;
    private readonly templateService;
    private readonly logger;
    constructor(httpService: HttpService, configService: ConfigService, templateService: TemplateService);
    private sendEmail;
    sendUserCreationEmail(email: string, firstName: string, verificationToken: string): Promise<void>;
    sendPasswordResetEmail(email: string, firstName: string, resetToken: string): Promise<void>;
    send2FASetupEmail(email: string, firstName: string): Promise<void>;
    sendAccountVerificationEmail(email: string, firstName: string, verificationToken: string): Promise<void>;
}
