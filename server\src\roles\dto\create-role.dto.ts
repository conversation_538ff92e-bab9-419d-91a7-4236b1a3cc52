import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsA<PERSON>y,
  <PERSON>E<PERSON>,
  MinLength,
  IsOptional,
} from 'class-validator';
import { Privilege } from '@prisma/client';

export class CreateRoleDto {
  @ApiProperty({
    description: 'Role name',
    example: 'Data Collector',
    minLength: 2,
  })
  @IsString()
  @MinLength(2)
  name: string;

  @ApiProperty({
    description: 'Role code (unique identifier)',
    example: 'DATA_COLLECTOR',
    minLength: 2,
  })
  @IsString()
  @MinLength(2)
  code: string;

  @ApiProperty({
    description: 'List of privileges assigned to this role',
    example: [Privilege.DATA_COLLECTION],
    enum: Privilege,
    isArray: true,
  })
  @IsArray()
  @IsEnum(Privilege, { each: true })
  privileges: Privilege[];
}

export class CreateRoleResponseDto {
  @ApiProperty({
    description: 'Created role information',
  })
  role: {
    id: string;
    name: string;
    code: string;
    privileges: Privilege[];
    createdAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Role created successfully',
  })
  message: string;
}
