"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VillagesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let VillagesService = class VillagesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createVillageDto) {
        const { code, name, cellId } = createVillageDto;
        const cell = await this.prisma.cell.findUnique({
            where: { id: cellId },
        });
        if (!cell) {
            throw new common_1.NotFoundException('Cell not found');
        }
        const existingVillage = await this.prisma.village.findFirst({
            where: {
                OR: [
                    { code },
                    { name },
                ],
            },
        });
        if (existingVillage) {
            if (existingVillage.code === code) {
                throw new common_1.ConflictException('Village with this code already exists');
            }
            if (existingVillage.name === name) {
                throw new common_1.ConflictException('Village with this name already exists');
            }
        }
        const village = await this.prisma.village.create({
            data: {
                code,
                name,
                cellId,
            },
        });
        return {
            village: {
                id: village.id,
                code: village.code,
                name: village.name,
                cellId: village.cellId,
                createdAt: village.createdAt,
            },
            message: 'Village created successfully',
        };
    }
    async findAll(page = 1, limit = 10, search, cellId) {
        const skip = (page - 1) * limit;
        const where = {
            AND: [
                search
                    ? {
                        OR: [
                            { name: { contains: search, mode: 'insensitive' } },
                            { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                        ].filter(Boolean),
                    }
                    : {},
                cellId ? { cellId } : {},
            ],
        };
        const [villages, total] = await Promise.all([
            this.prisma.village.findMany({
                where,
                skip,
                take: limit,
                include: {
                    cell: {
                        include: {
                            sector: {
                                include: {
                                    district: {
                                        include: {
                                            province: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    code: 'asc',
                },
            }),
            this.prisma.village.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            villages: villages.map((village) => ({
                id: village.id,
                code: village.code,
                name: village.name,
                cell: {
                    id: village.cell.id,
                    name: village.cell.name,
                    code: village.cell.code,
                    sector: {
                        id: village.cell.sector.id,
                        name: village.cell.sector.name,
                        code: village.cell.sector.code,
                        district: {
                            id: village.cell.sector.district.id,
                            name: village.cell.sector.district.name,
                            code: village.cell.sector.district.code,
                            province: {
                                id: village.cell.sector.district.province.id,
                                name: village.cell.sector.district.province.name,
                                code: village.cell.sector.district.province.code,
                            },
                        },
                    },
                },
                createdAt: village.createdAt,
                updatedAt: village.updatedAt,
            })),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const village = await this.prisma.village.findUnique({
            where: { id },
            include: {
                cell: {
                    include: {
                        sector: {
                            include: {
                                district: {
                                    include: {
                                        province: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!village) {
            throw new common_1.NotFoundException('Village not found');
        }
        return {
            id: village.id,
            code: village.code,
            name: village.name,
            cell: {
                id: village.cell.id,
                name: village.cell.name,
                code: village.cell.code,
                sector: {
                    id: village.cell.sector.id,
                    name: village.cell.sector.name,
                    code: village.cell.sector.code,
                    district: {
                        id: village.cell.sector.district.id,
                        name: village.cell.sector.district.name,
                        code: village.cell.sector.district.code,
                        province: {
                            id: village.cell.sector.district.province.id,
                            name: village.cell.sector.district.province.name,
                            code: village.cell.sector.district.province.code,
                        },
                    },
                },
            },
            createdAt: village.createdAt,
            updatedAt: village.updatedAt,
        };
    }
    async update(id, updateVillageDto) {
        const { code, name, cellId } = updateVillageDto;
        const existingVillage = await this.prisma.village.findUnique({
            where: { id },
        });
        if (!existingVillage) {
            throw new common_1.NotFoundException('Village not found');
        }
        if (cellId) {
            const cell = await this.prisma.cell.findUnique({
                where: { id: cellId },
            });
            if (!cell) {
                throw new common_1.NotFoundException('Cell not found');
            }
        }
        if (code || name) {
            const conflictVillage = await this.prisma.village.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        {
                            OR: [
                                code ? { code } : {},
                                name ? { name } : {},
                            ].filter(Boolean),
                        },
                    ],
                },
            });
            if (conflictVillage) {
                if (conflictVillage.code === code) {
                    throw new common_1.ConflictException('Village with this code already exists');
                }
                if (conflictVillage.name === name) {
                    throw new common_1.ConflictException('Village with this name already exists');
                }
            }
        }
        const updatedVillage = await this.prisma.village.update({
            where: { id },
            data: {
                ...(code && { code }),
                ...(name && { name }),
                ...(cellId && { cellId }),
            },
        });
        return {
            village: {
                id: updatedVillage.id,
                code: updatedVillage.code,
                name: updatedVillage.name,
                cellId: updatedVillage.cellId,
                updatedAt: updatedVillage.updatedAt,
            },
            message: 'Village updated successfully',
        };
    }
    async remove(id) {
        const existingVillage = await this.prisma.village.findUnique({
            where: { id },
        });
        if (!existingVillage) {
            throw new common_1.NotFoundException('Village not found');
        }
        await this.prisma.village.delete({
            where: { id },
        });
        return { message: 'Village deleted successfully' };
    }
};
exports.VillagesService = VillagesService;
exports.VillagesService = VillagesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VillagesService);
//# sourceMappingURL=villages.service.js.map