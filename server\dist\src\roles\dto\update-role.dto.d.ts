import { CreateRoleDto } from './create-role.dto';
import { Privilege } from '@prisma/client';
declare const UpdateRoleDto_base: import("@nestjs/common").Type<Partial<CreateRoleDto>>;
export declare class UpdateRoleDto extends UpdateRoleDto_base {
}
export declare class UpdateRoleResponseDto {
    role: {
        id: string;
        name: string;
        code: string;
        privileges: Privilege[];
        updatedAt: Date;
    };
    message: string;
}
export {};
