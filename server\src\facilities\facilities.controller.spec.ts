import { Test, TestingModule } from '@nestjs/testing';
import { FacilitiesController } from './facilities.controller';
import { FacilitiesService } from './facilities.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { PrivilegeGuard } from '../common/guards/privilege.guard';
import { MobileGuard } from '../common/guards/mobile.guard';

describe('FacilitiesController', () => {
  let controller: FacilitiesController;
  let service: FacilitiesService;

  const mockFacilitiesService = {
    createHousehold: jest.fn(),
    findAllHouseholds: jest.fn(),
    findOneHousehold: jest.fn(),
    updateHousehold: jest.fn(),
    removeHousehold: jest.fn(),
    createSchool: jest.fn(),
    findAllSchools: jest.fn(),
    findOneSchool: jest.fn(),
    updateSchool: jest.fn(),
    removeSchool: jest.fn(),
    createHealthFacility: jest.fn(),
    findAllHealthFacilities: jest.fn(),
    findOneHealthFacility: jest.fn(),
    updateHealthFacility: jest.fn(),
    removeHealthFacility: jest.fn(),
    createMarket: jest.fn(),
    findAllMarkets: jest.fn(),
    findOneMarket: jest.fn(),
    updateMarket: jest.fn(),
    removeMarket: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FacilitiesController],
      providers: [
        {
          provide: FacilitiesService,
          useValue: mockFacilitiesService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(PrivilegeGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(MobileGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<FacilitiesController>(FacilitiesController);
    service = module.get<FacilitiesService>(FacilitiesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Household Endpoints', () => {
    const mockHouseholdResponse = {
      facility: {
        id: 'household_123',
        number: 1001,
        location: {
          id: 'location_123',
          villageId: 1,
          village: {
            id: 1,
            name: 'Test Village',
            cell: {
              id: 1,
              name: 'Test Cell',
              sector: {
                id: 1,
                name: 'Test Sector',
                district: {
                  id: 1,
                  name: 'Test District',
                  province: {
                    id: 1,
                    name: 'Test Province',
                  },
                },
              },
            },
          },
          latitude: -1.9441,
          longitude: 30.0619,
          settlementType: 'URBAN',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      message: 'Household created successfully',
    };

    describe('POST /facilities/households', () => {
      it('should create a household', async () => {
        const createDto = {
          locationId: 'location_123',
          number: 1001,
        };

        mockFacilitiesService.createHousehold.mockResolvedValue(mockHouseholdResponse);

        const result = await controller.createHousehold(createDto);

        expect(service.createHousehold).toHaveBeenCalledWith(createDto);
        expect(result).toEqual(mockHouseholdResponse);
      });
    });

    describe('GET /facilities/households', () => {
      it('should return paginated households', async () => {
        const mockListResponse = {
          data: [mockHouseholdResponse.facility],
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        };

        mockFacilitiesService.findAllHouseholds.mockResolvedValue(mockListResponse);

        const query = { page: 1, limit: 10, search: '1001' };
        const result = await controller.findAllHouseholds(query);

        expect(service.findAllHouseholds).toHaveBeenCalledWith(1, 10, '1001');
        expect(result).toEqual(mockListResponse);
      });
    });

    describe('GET /facilities/households/:id', () => {
      it('should return a household by ID', async () => {
        mockFacilitiesService.findOneHousehold.mockResolvedValue(mockHouseholdResponse.facility);

        const result = await controller.findOneHousehold('household_123');

        expect(service.findOneHousehold).toHaveBeenCalledWith('household_123');
        expect(result).toEqual(mockHouseholdResponse.facility);
      });
    });

    describe('PUT /facilities/households/:id', () => {
      it('should update a household', async () => {
        const updateDto = { number: 1002 };
        const updatedResponse = {
          ...mockHouseholdResponse,
          facility: { ...mockHouseholdResponse.facility, number: 1002 },
          message: 'Household updated successfully',
        };

        mockFacilitiesService.updateHousehold.mockResolvedValue(updatedResponse);

        const result = await controller.updateHousehold('household_123', updateDto);

        expect(service.updateHousehold).toHaveBeenCalledWith('household_123', updateDto);
        expect(result).toEqual(updatedResponse);
      });
    });

    describe('DELETE /facilities/households/:id', () => {
      it('should soft delete a household', async () => {
        const deleteResponse = { message: 'Household deleted successfully' };
        mockFacilitiesService.removeHousehold.mockResolvedValue(deleteResponse);

        const result = await controller.removeHousehold('household_123');

        expect(service.removeHousehold).toHaveBeenCalledWith('household_123');
        expect(result).toEqual(deleteResponse);
      });
    });
  });

  describe('School Endpoints', () => {
    const mockSchoolResponse = {
      facility: {
        id: 'school_123',
        number: 2001,
        name: 'Test Primary School',
        location: {
          id: 'location_123',
          villageId: 1,
          village: {
            id: 1,
            name: 'Test Village',
            cell: {
              id: 1,
              name: 'Test Cell',
              sector: {
                id: 1,
                name: 'Test Sector',
                district: {
                  id: 1,
                  name: 'Test District',
                  province: {
                    id: 1,
                    name: 'Test Province',
                  },
                },
              },
            },
          },
          latitude: -1.9441,
          longitude: 30.0619,
          settlementType: 'URBAN',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      message: 'School created successfully',
    };

    describe('POST /facilities/schools', () => {
      it('should create a school', async () => {
        const createDto = {
          locationId: 'location_123',
          number: 2001,
          name: 'Test Primary School',
        };

        mockFacilitiesService.createSchool.mockResolvedValue(mockSchoolResponse);

        const result = await controller.createSchool(createDto);

        expect(service.createSchool).toHaveBeenCalledWith(createDto);
        expect(result).toEqual(mockSchoolResponse);
      });
    });

    describe('GET /facilities/schools', () => {
      it('should return paginated schools', async () => {
        const mockListResponse = {
          data: [mockSchoolResponse.facility],
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        };

        mockFacilitiesService.findAllSchools.mockResolvedValue(mockListResponse);

        const query = { page: 1, limit: 10, search: 'Primary' };
        const result = await controller.findAllSchools(query);

        expect(service.findAllSchools).toHaveBeenCalledWith(1, 10, 'Primary');
        expect(result).toEqual(mockListResponse);
      });
    });
  });

  describe('Health Facility Endpoints', () => {
    describe('POST /facilities/health-facilities', () => {
      it('should create a health facility', async () => {
        const createDto = {
          locationId: 'location_123',
          number: 3001,
          name: 'Test Health Center',
        };

        const mockResponse = {
          facility: {
            id: 'health_facility_123',
            number: 3001,
            name: 'Test Health Center',
            location: {},
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          message: 'Health facility created successfully',
        };

        mockFacilitiesService.createHealthFacility.mockResolvedValue(mockResponse);

        const result = await controller.createHealthFacility(createDto);

        expect(service.createHealthFacility).toHaveBeenCalledWith(createDto);
        expect(result).toEqual(mockResponse);
      });
    });
  });

  describe('Market Endpoints', () => {
    describe('POST /facilities/markets', () => {
      it('should create a market', async () => {
        const createDto = {
          locationId: 'location_123',
          number: 4001,
          name: 'Test Central Market',
        };

        const mockResponse = {
          facility: {
            id: 'market_123',
            number: 4001,
            name: 'Test Central Market',
            location: {},
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          message: 'Market created successfully',
        };

        mockFacilitiesService.createMarket.mockResolvedValue(mockResponse);

        const result = await controller.createMarket(createDto);

        expect(service.createMarket).toHaveBeenCalledWith(createDto);
        expect(result).toEqual(mockResponse);
      });
    });
  });
});
