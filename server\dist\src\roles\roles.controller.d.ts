import { RolesService } from './roles.service';
import { CreateRoleDto, CreateRoleResponseDto } from './dto/create-role.dto';
import { UpdateRoleDto, UpdateRoleResponseDto } from './dto/update-role.dto';
import { RoleResponseDto, RolesListResponseDto } from './dto/role-response.dto';
export declare class RolesController {
    private readonly rolesService;
    constructor(rolesService: RolesService);
    create(createRoleDto: CreateRoleDto): Promise<CreateRoleResponseDto>;
    findAll(page?: number, limit?: number, search?: string): Promise<RolesListResponseDto>;
    findOne(id: string): Promise<RoleResponseDto>;
    update(id: string, updateRoleDto: UpdateRoleDto): Promise<UpdateRoleResponseDto>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
