import { PrismaService } from '../prisma/prisma.service';
import { EmailService } from '../email/email.service';
import { CreateUserDto, CreateUserResponseDto } from './dto/create-user.dto';
import { UpdateUserDto, UpdateUserResponseDto } from './dto/update-user.dto';
import { UserResponseDto, UsersListResponseDto } from './dto/user-response.dto';
export declare class UsersService {
    private prisma;
    private emailService;
    constructor(prisma: PrismaService, emailService: EmailService);
    create(createUserDto: CreateUserDto): Promise<CreateUserResponseDto>;
    findAll(page?: number, limit?: number, search?: string, roleId?: string): Promise<UsersListResponseDto>;
    findOne(id: string): Promise<UserResponseDto>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<UpdateUserResponseDto>;
    remove(id: string): Promise<{
        message: string;
    }>;
    resendVerificationEmail(id: string): Promise<{
        message: string;
    }>;
    private validateLocationAccess;
}
