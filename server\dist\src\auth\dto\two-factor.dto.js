"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Enable2FAResponseDto = exports.Setup2FAResponseDto = exports.UseRecoveryCodeDto = exports.Verify2FADto = exports.Setup2FADto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class Setup2FADto {
    totpCode;
}
exports.Setup2FADto = Setup2FADto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '6-digit TOTP code from authenticator app',
        example: '123456',
        minLength: 6,
        maxLength: 6,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    (0, class_validator_1.MaxLength)(6),
    __metadata("design:type", String)
], Setup2FADto.prototype, "totpCode", void 0);
class Verify2FADto {
    tempToken;
    totpCode;
}
exports.Verify2FADto = Verify2FADto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Temporary token from login attempt',
        example: 'temp_token_123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Verify2FADto.prototype, "tempToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '6-digit TOTP code from authenticator app',
        example: '123456',
        minLength: 6,
        maxLength: 6,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    (0, class_validator_1.MaxLength)(6),
    __metadata("design:type", String)
], Verify2FADto.prototype, "totpCode", void 0);
class UseRecoveryCodeDto {
    tempToken;
    recoveryCode;
}
exports.UseRecoveryCodeDto = UseRecoveryCodeDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Temporary token from login attempt',
        example: 'temp_token_123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UseRecoveryCodeDto.prototype, "tempToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recovery code',
        example: 'ABCD-1234-EFGH-5678',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UseRecoveryCodeDto.prototype, "recoveryCode", void 0);
class Setup2FAResponseDto {
    secret;
    qrCode;
    manualEntryKey;
}
exports.Setup2FAResponseDto = Setup2FAResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Base32 encoded secret for manual entry',
        example: 'JBSWY3DPEHPK3PXP',
    }),
    __metadata("design:type", String)
], Setup2FAResponseDto.prototype, "secret", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'QR code data URL for scanning',
        example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
    }),
    __metadata("design:type", String)
], Setup2FAResponseDto.prototype, "qrCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Backup URL for manual entry',
        example: 'otpauth://totp/MyApp:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MyApp',
    }),
    __metadata("design:type", String)
], Setup2FAResponseDto.prototype, "manualEntryKey", void 0);
class Enable2FAResponseDto {
    message;
    recoveryCodes;
}
exports.Enable2FAResponseDto = Enable2FAResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: '2FA has been enabled successfully',
    }),
    __metadata("design:type", String)
], Enable2FAResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recovery codes for backup access',
        example: ['ABCD-1234-EFGH-5678', 'IJKL-9012-MNOP-3456'],
        type: [String],
    }),
    __metadata("design:type", Array)
], Enable2FAResponseDto.prototype, "recoveryCodes", void 0);
//# sourceMappingURL=two-factor.dto.js.map