"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let RolesService = class RolesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createRoleDto) {
        const { name, code, privileges } = createRoleDto;
        const existingRole = await this.prisma.role.findFirst({
            where: {
                OR: [
                    { name },
                    { code },
                ],
            },
        });
        if (existingRole) {
            if (existingRole.name === name) {
                throw new common_1.ConflictException('Role with this name already exists');
            }
            if (existingRole.code === code) {
                throw new common_1.ConflictException('Role with this code already exists');
            }
        }
        const role = await this.prisma.role.create({
            data: {
                name,
                code,
                privileges,
            },
        });
        return {
            role: {
                id: role.id,
                name: role.name,
                code: role.code,
                privileges: role.privileges,
                createdAt: role.createdAt,
            },
            message: 'Role created successfully',
        };
    }
    async findAll(page = 1, limit = 10, search) {
        const skip = (page - 1) * limit;
        const where = search
            ? {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { code: { contains: search, mode: 'insensitive' } },
                ],
            }
            : {};
        const [roles, total] = await Promise.all([
            this.prisma.role.findMany({
                where,
                skip,
                take: limit,
                include: {
                    _count: {
                        select: { users: true },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.role.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            roles: roles.map((role) => ({
                id: role.id,
                name: role.name,
                code: role.code,
                privileges: role.privileges,
                userCount: role._count.users,
                createdAt: role.createdAt,
                updatedAt: role.updatedAt,
            })),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const role = await this.prisma.role.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { users: true },
                },
            },
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        return {
            id: role.id,
            name: role.name,
            code: role.code,
            privileges: role.privileges,
            userCount: role._count.users,
            createdAt: role.createdAt,
            updatedAt: role.updatedAt,
        };
    }
    async update(id, updateRoleDto) {
        const { name, code, privileges } = updateRoleDto;
        const existingRole = await this.prisma.role.findUnique({
            where: { id },
        });
        if (!existingRole) {
            throw new common_1.NotFoundException('Role not found');
        }
        if (name || code) {
            const conflictRole = await this.prisma.role.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        {
                            OR: [
                                name ? { name } : {},
                                code ? { code } : {},
                            ].filter(Boolean),
                        },
                    ],
                },
            });
            if (conflictRole) {
                if (conflictRole.name === name) {
                    throw new common_1.ConflictException('Role with this name already exists');
                }
                if (conflictRole.code === code) {
                    throw new common_1.ConflictException('Role with this code already exists');
                }
            }
        }
        const updatedRole = await this.prisma.role.update({
            where: { id },
            data: {
                ...(name && { name }),
                ...(code && { code }),
                ...(privileges && { privileges }),
            },
        });
        return {
            role: {
                id: updatedRole.id,
                name: updatedRole.name,
                code: updatedRole.code,
                privileges: updatedRole.privileges,
                updatedAt: updatedRole.updatedAt,
            },
            message: 'Role updated successfully',
        };
    }
    async remove(id) {
        const existingRole = await this.prisma.role.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { users: true },
                },
            },
        });
        if (!existingRole) {
            throw new common_1.NotFoundException('Role not found');
        }
        if (existingRole._count.users > 0) {
            throw new common_1.BadRequestException(`Cannot delete role. ${existingRole._count.users} user(s) are assigned to this role`);
        }
        await this.prisma.role.delete({
            where: { id },
        });
        return { message: 'Role deleted successfully' };
    }
};
exports.RolesService = RolesService;
exports.RolesService = RolesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RolesService);
//# sourceMappingURL=roles.service.js.map