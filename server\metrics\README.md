# WASH MIS LGTM Stack Monitoring

This directory contains a comprehensive monitoring setup for the WASH MIS NestJS backend application using the LGTM stack (Loki, Graf<PERSON>, Tempo, Mimir).

## Architecture Overview

The monitoring stack consists of:

- **Mimir**: Metrics storage and querying (replaces Prometheus for better scalability)
- **Prometheus**: Metrics scraping and forwarding to Mimir
- **Loki**: Log aggregation and storage
- **Tempo**: Distributed tracing storage and querying
- **Grafana**: Visualization and dashboards
- **Promtail**: Log collection (optional, for file-based logs)

## Quick Start

1. **Start the monitoring stack:**
   ```bash
   cd metrics
   docker-compose up -d
   ```

2. **Start the WASH MIS application:**
   ```bash
   cd ..
   pnpm run dev
   ```

3. **Access the services:**
   - Grafana: http://localhost:3001 (admin/admin)
   - Prometheus: http://localhost:9090
   - Mimir: http://localhost:9009
   - Loki: http://localhost:3100
   - Tempo: http://localhost:3200

## Configuration Files

### Core Services
- `docker-compose.yml`: Main orchestration file
- `config/mimir.yml`: Mimir configuration
- `config/prometheus.yml`: Prometheus scraping configuration
- `config/loki.yml`: Loki log aggregation configuration
- `config/tempo.yml`: Tempo tracing configuration
- `config/promtail.yml`: Promtail log collection configuration

### Grafana
- `config/grafana/provisioning/datasources/`: Data source configurations
- `config/grafana/provisioning/dashboards/`: Dashboard provisioning
- `config/grafana/dashboards/`: Dashboard JSON files

### Alerting
- `config/prometheus/rules/`: Prometheus alerting rules

## Dashboards

### WASH MIS Dashboards
1. **Overview Dashboard** (`wash-mis-overview`): High-level system metrics
2. **Business Metrics** (`wash-mis-business`): WASH MIS specific KPIs
   - Facility counts (households, schools, health facilities, markets)
   - Data submission rates
   - Authentication metrics
   - User registration trends

### Application Dashboards
1. **NestJS Performance** (`nestjs-performance`): Application performance metrics
   - HTTP request duration and rates
   - Memory usage
   - Error rates
   - Database query performance

### Infrastructure Dashboards
1. **LGTM Stack** (`lgtm-infrastructure`): Monitoring stack health
   - Service availability
   - Resource usage
   - Request rates

## Metrics Collected

### HTTP Metrics
- `wash_mis_http_request_duration_seconds`: Request duration histogram
- `wash_mis_http_requests_total`: Total HTTP requests counter
- `wash_mis_http_active_connections`: Active HTTP connections gauge

### Authentication Metrics
- `wash_mis_auth_attempts_total`: Authentication attempts by type and status
- `wash_mis_active_user_sessions`: Active user sessions by role

### Business Metrics
- `wash_mis_data_submissions_total`: Data submissions by facility type and status
- `wash_mis_user_registrations_total`: User registrations by role
- `wash_mis_facilities_total`: Facility counts by type and location
- `wash_mis_location_coverage`: Coverage metrics by location

### Database Metrics
- `wash_mis_database_query_duration_seconds`: Database query duration
- `wash_mis_database_connections`: Database connection states

### Error Metrics
- `wash_mis_errors_total`: Application errors by type and severity
- `wash_mis_emails_sent_total`: Email sending metrics

## Alerting Rules

The system includes comprehensive alerting rules:

### Application Alerts
- High HTTP error rate (>10% for 2 minutes)
- High response time (95th percentile >2s for 5 minutes)
- High memory usage (>512MB for 5 minutes)
- Application down (>1 minute)
- High authentication failure rate (>30% for 3 minutes)
- Slow database queries (95th percentile >1s for 5 minutes)

### Business Alerts
- Low data submission rate (<0.1/s for 30 minutes)
- High data submission failure rate (>20% for 10 minutes)
- Email sending failures (>0.1/s for 5 minutes)

### Infrastructure Alerts
- Service down alerts for all LGTM components
- High disk usage (>85% for 5 minutes)

## Tracing

The application is instrumented with OpenTelemetry for distributed tracing:

- **HTTP requests**: Automatic instrumentation
- **Database queries**: Custom spans with query details
- **Business operations**: Custom spans for WASH MIS operations
- **Authentication flows**: Detailed tracing of auth processes

Traces include:
- Request/response details
- Database query information
- Error information
- Custom business context

## Logging

Structured logging with correlation to traces:

- **Format**: JSON with trace correlation
- **Levels**: DEBUG, INFO, WARN, ERROR
- **Destinations**: Console (dev), Loki (production), Files (backup)
- **Correlation**: Trace ID and Span ID included in logs

## Data Retention

### Metrics (Mimir)
- Raw data: 30 days
- Downsampled data: 1 year (configurable)

### Logs (Loki)
- Retention: 30 days
- Compression: Enabled

### Traces (Tempo)
- Retention: 1 hour (configurable)
- Sampling: 100% (adjust for production)

## Production Considerations

### Security
- Change default Grafana credentials
- Enable authentication for all services
- Use TLS for external access
- Implement network segmentation

### Performance
- Adjust retention policies based on storage capacity
- Configure appropriate resource limits
- Enable compression for all services
- Implement proper backup strategies

### Scaling
- Use external storage backends for production
- Implement clustering for high availability
- Configure load balancing
- Monitor resource usage and scale accordingly

## Troubleshooting

### Common Issues

1. **Services not starting**: Check Docker logs and port conflicts
2. **No metrics**: Verify Prometheus scraping configuration
3. **No logs**: Check Loki configuration and network connectivity
4. **No traces**: Verify OpenTelemetry configuration and endpoints

### Useful Commands

```bash
# Check service status
docker-compose ps

# View service logs
docker-compose logs -f [service-name]

# Restart specific service
docker-compose restart [service-name]

# Check metrics endpoint
curl http://localhost:8080/api/v1/metrics

# Check Loki logs
curl http://localhost:3100/ready
```

## Development

### Adding New Metrics
1. Define metric in `src/metrics.ts`
2. Use metric in application code
3. Update dashboards if needed
4. Add alerting rules if appropriate

### Adding New Dashboards
1. Create dashboard in Grafana UI
2. Export JSON
3. Save to appropriate directory
4. Update provisioning configuration

### Custom Instrumentation
Use the `MonitoringService` for business-specific monitoring:

```typescript
// Inject the service
constructor(private monitoring: MonitoringService) {}

// Record business events
this.monitoring.recordDataSubmission('household', 'rural', 'success');

// Track operations with tracing
await this.monitoring.trackBusinessOperation(
  'process-submission',
  async () => {
    // Your business logic here
  },
  { facility_type: 'school', location: 'kigali' }
);
```
