# WASH MIS Backend Server

NestJS backend API for the Water, Sanitation, and Hygiene Management Information System.

## Running Instructions

### Local Development

1. **Install dependencies**
   ```bash
   pnpm install
   ```

2. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your database and configuration
   ```

3. **Setup database**
   ```bash
   npx prisma db push --schema ./prisma/schema
   # npx prisma generate
   # npx prisma db push
   pnpm run seed
   ```

4. **Start development server**
   ```bash
   pnpm run dev
   ```

### Docker (Optional)

1. **Build and run with Docker**
   ```bash
   docker build -t wash-mis-server .
   docker run -p 3000:3000 wash-mis-server
   ```

2. **Or use Docker Compose**
   ```bash
   docker-compose up
   ```

## Access Points

- **API Server**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api-docs

## Key Environment Variables

```env
DATABASE_URL="postgresql://user:pass@localhost:5432/wash_mis_db"
JWT_SECRET="your-jwt-secret"
FRONTEND_URL="http://localhost:4200"
SUPPORT_EMAIL="<EMAIL>"
```

## Development

- **Run tests**: `pnpm run test`
- **Build**: `pnpm run build`
- **Production**: `pnpm run start:prod`
- **Database migrations**: `npx prisma db push`
- **API docs**: Available at `/api-docs` when server is running
