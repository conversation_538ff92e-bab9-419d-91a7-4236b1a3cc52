"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSectorResponseDto = exports.UpdateSectorDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_sector_dto_1 = require("./create-sector.dto");
class UpdateSectorDto extends (0, swagger_1.PartialType)(create_sector_dto_1.CreateSectorDto) {
}
exports.UpdateSectorDto = UpdateSectorDto;
class UpdateSectorResponseDto {
    sector;
    message;
}
exports.UpdateSectorResponseDto = UpdateSectorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated sector information',
    }),
    __metadata("design:type", Object)
], UpdateSectorResponseDto.prototype, "sector", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Sector updated successfully',
    }),
    __metadata("design:type", String)
], UpdateSectorResponseDto.prototype, "message", void 0);
//# sourceMappingURL=update-sector.dto.js.map