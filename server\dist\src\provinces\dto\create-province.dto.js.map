{"version": 3, "file": "create-province.dto.js", "sourceRoot": "", "sources": ["../../../../src/provinces/dto/create-province.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAKyB;AAEzB,MAAa,iBAAiB;IAQ5B,IAAI,CAAS;IASb,IAAI,CAAS;CACd;AAlBD,8CAkBC;AAVC;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mCAAmC;QAChD,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;;+CACM;AASb;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,eAAe;QAC5B,OAAO,EAAE,aAAa;QACtB,SAAS,EAAE,CAAC;KACb,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,CAAC;;+CACA;AAGf,MAAa,yBAAyB;IAIpC,QAAQ,CAKN;IAMF,OAAO,CAAS;CACjB;AAhBD,8DAgBC;AAZC;IAHC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;KAC5C,CAAC;;2DAMA;AAMF;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,+BAA+B;KACzC,CAAC;;0DACc"}