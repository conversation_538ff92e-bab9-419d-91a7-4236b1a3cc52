{"version": 3, "file": "template.service.js", "sourceRoot": "", "sources": ["../../../src/email/template.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,uDAAyC;AACzC,2BAAkC;AAClC,+BAA4B;AAOrB,IAAM,eAAe,uBAArB,MAAM,eAAe;IAKG;IAJZ,MAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;IAC1C,YAAY,CAAS;IACrB,iBAAiB,GAAG,IAAI,GAAG,EAAsC,CAAC;IAEnF,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QACvD,IAAI,CAAC,YAAY,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QACjD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,eAAe;IAGvB,CAAC;IAEO,WAAW,CAAC,YAAoB;QACtC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;QACnD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAA,WAAI,EAAC,IAAI,CAAC,YAAY,EAAE,GAAG,YAAY,MAAM,CAAC,CAAC;YACpE,MAAM,eAAe,GAAG,IAAA,iBAAY,EAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAC5D,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAE7D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;YAC3D,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,YAAY,YAAY,qCAAqC,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAIO,cAAc;QACpB,OAAO;YACL,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YAC9B,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC;YAC3C,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;YACrD,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;YACrD,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC;YACnD,WAAW,EAAE;gBACX,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC;gBAChD,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC;aACjD;YACD,cAAc,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;YAC3D,cAAc,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;SAC5D,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,YAAoB,EAAE,OAAwB;QACjE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAEhD,MAAM,WAAW,GAAG;gBAClB,GAAG,IAAI,CAAC,cAAc,EAAE;gBACxB,GAAG,OAAO;aACX,CAAC;YAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;YAE3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,YAAY,wBAAwB,CAAC,CAAC;YACpE,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAC5C,CAAC;CACF,CAAA;AA3EY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAMiC,sBAAa;GAL9C,eAAe,CA2E3B"}