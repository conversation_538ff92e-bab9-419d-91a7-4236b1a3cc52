"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersListResponseDto = exports.UserResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UserResponseDto {
    id;
    firstName;
    lastName;
    email;
    telephoneNumber;
    role;
    accountVerified;
    is2FAEnabled;
    locations;
    createdAt;
    updatedAt;
}
exports.UserResponseDto = UserResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID',
        example: 'user_123',
    }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User first name',
        example: 'John',
    }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User last name',
        example: 'Doe',
    }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User email address',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User telephone number',
        example: '+************',
    }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "telephoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User role information',
    }),
    __metadata("design:type", Object)
], UserResponseDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Account verification status',
        example: true,
    }),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "accountVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '2FA enabled status',
        example: false,
    }),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "is2FAEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User location access',
        type: [Object],
    }),
    __metadata("design:type", Array)
], UserResponseDto.prototype, "locations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], UserResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], UserResponseDto.prototype, "updatedAt", void 0);
class UsersListResponseDto {
    users;
    total;
    page;
    limit;
    totalPages;
}
exports.UsersListResponseDto = UsersListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of users',
        type: [UserResponseDto],
    }),
    __metadata("design:type", Array)
], UsersListResponseDto.prototype, "users", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of users',
        example: 100,
    }),
    __metadata("design:type", Number)
], UsersListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page',
        example: 1,
    }),
    __metadata("design:type", Number)
], UsersListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], UsersListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 10,
    }),
    __metadata("design:type", Number)
], UsersListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=user-response.dto.js.map