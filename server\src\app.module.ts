import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { EmailModule } from './email/email.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { RolesModule } from './roles/roles.module';
import { ProvincesModule } from './provinces/provinces.module';
import { DistrictsModule } from './districts/districts.module';
import { SectorsModule } from './sectors/sectors.module';
import { CellsModule } from './cells/cells.module';
import { VillagesModule } from './villages/villages.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      cache: true,
      isGlobal: true
    }),
    PrismaModule,
    EmailModule,
    AuthModule,
    UsersModule,
    MonitoringModule,
    RolesModule,
    ProvincesModule,
    DistrictsModule,
    SectorsModule,
    CellsModule,
    VillagesModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
  ],
})
export class AppModule { }
