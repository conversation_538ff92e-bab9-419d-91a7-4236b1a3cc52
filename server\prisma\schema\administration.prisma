model Province {
    id        Int        @id @default(autoincrement())
    code      Int        @unique
    name      String
    districts District[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    UserLocationAccess UserLocationAccess[]
}

model District {
    id         Int      @id @default(autoincrement())
    code       Int      @unique
    name       String
    provinceId Int
    province   Province @relation(fields: [provinceId], references: [id])
    sectors    Sector[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    UserLocationAccess UserLocationAccess[]
}

model Sector {
    id         Int      @id @default(autoincrement())
    code       Int      @unique
    name       String
    districtId Int
    district   District @relation(fields: [districtId], references: [id])
    cells      Cell[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    UserLocationAccess UserLocationAccess[]
}

model Cell {
    id       Int       @id @default(autoincrement())
    code     Int       @unique
    name     String
    sectorId Int
    sector   Sector    @relation(fields: [sectorId], references: [id])
    villages Village[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    UserLocationAccess UserLocationAccess[]
}

model Village {
    id     Int    @id @default(autoincrement())
    code   Int    @unique
    name   String
    cellId Int
    cell   Cell   @relation(fields: [cellId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    locationAccess UserLocationAccess[]

    Location Location[]
}
