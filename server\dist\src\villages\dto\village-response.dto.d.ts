export declare class VillageResponseDto {
    id: number;
    code: number;
    name: string;
    cell: {
        id: number;
        name: string;
        code: number;
        sector: {
            id: number;
            name: string;
            code: number;
            district: {
                id: number;
                name: string;
                code: number;
                province: {
                    id: number;
                    name: string;
                    code: number;
                };
            };
        };
    };
    createdAt: Date;
    updatedAt: Date;
}
export declare class VillagesListResponseDto {
    villages: VillageResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
