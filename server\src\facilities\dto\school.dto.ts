import { ApiProperty, PartialType } from '@nestjs/swagger';
import { 
  BaseFacilityWithNameDto, 
  BaseFacilityWithNameResponseDto, 
  BasePaginatedResponseDto,
  BaseCreateResponseDto,
  BaseUpdateResponseDto
} from './base-facility.dto';

export class CreateSchoolDto extends BaseFacilityWithNameDto {}

export class UpdateSchoolDto extends PartialType(CreateSchoolDto) {}

export class SchoolResponseDto extends BaseFacilityWithNameResponseDto {}

export class SchoolsListResponseDto extends BasePaginatedResponseDto<SchoolResponseDto> {
  @ApiProperty({
    description: 'List of schools',
    type: [SchoolResponseDto],
  })
  declare data: SchoolResponseDto[];
}

export class CreateSchoolResponseDto extends BaseCreateResponseDto<SchoolResponseDto> {
  @ApiProperty({
    description: 'Created school information',
  })
  declare facility: SchoolResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'School created successfully',
  })
  declare message: string;
}

export class UpdateSchoolResponseDto extends BaseUpdateResponseDto<SchoolResponseDto> {
  @ApiProperty({
    description: 'Updated school information',
  })
  declare facility: SchoolResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'School updated successfully',
  })
  declare message: string;
}
