import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateDistrictDto,
  CreateDistrictResponseDto,
} from './dto/create-district.dto';
import {
  UpdateDistrictDto,
  UpdateDistrictResponseDto,
} from './dto/update-district.dto';
import {
  DistrictResponseDto,
  DistrictsListResponseDto,
} from './dto/district-response.dto';

@Injectable()
export class DistrictsService {
  constructor(private prisma: PrismaService) {}

  async create(createDistrictDto: CreateDistrictDto): Promise<CreateDistrictResponseDto> {
    const { code, name, provinceId } = createDistrictDto;

    // Check if province exists
    const province = await this.prisma.province.findUnique({
      where: { id: provinceId },
    });

    if (!province) {
      throw new NotFoundException('Province not found');
    }

    // Check if district with same code or name already exists
    const existingDistrict = await this.prisma.district.findFirst({
      where: {
        OR: [
          { code },
          { name },
        ],
      },
    });

    if (existingDistrict) {
      if (existingDistrict.code === code) {
        throw new ConflictException('District with this code already exists');
      }
      if (existingDistrict.name === name) {
        throw new ConflictException('District with this name already exists');
      }
    }

    // Create district
    const district = await this.prisma.district.create({
      data: {
        code,
        name,
        provinceId,
      },
    });

    return {
      district: {
        id: district.id,
        code: district.code,
        name: district.name,
        provinceId: district.provinceId,
        createdAt: district.createdAt,
      },
      message: 'District created successfully',
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    provinceId?: number,
  ): Promise<DistrictsListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      AND: [
        search
          ? {
              OR: [
                { name: { contains: search, mode: 'insensitive' as const } },
                { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
              ].filter(Boolean),
            }
          : {},
        provinceId ? { provinceId } : {},
      ],
    };

    const [districts, total] = await Promise.all([
      this.prisma.district.findMany({
        where,
        skip,
        take: limit,
        include: {
          province: true,
          _count: {
            select: { sectors: true },
          },
        },
        orderBy: {
          code: 'asc',
        },
      }),
      this.prisma.district.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      districts: districts.map((district) => ({
        id: district.id,
        code: district.code,
        name: district.name,
        province: {
          id: district.province.id,
          name: district.province.name,
          code: district.province.code,
        },
        sectorCount: district._count.sectors,
        createdAt: district.createdAt,
        updatedAt: district.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: number): Promise<DistrictResponseDto> {
    const district = await this.prisma.district.findUnique({
      where: { id },
      include: {
        province: true,
        _count: {
          select: { sectors: true },
        },
      },
    });

    if (!district) {
      throw new NotFoundException('District not found');
    }

    return {
      id: district.id,
      code: district.code,
      name: district.name,
      province: {
        id: district.province.id,
        name: district.province.name,
        code: district.province.code,
      },
      sectorCount: district._count.sectors,
      createdAt: district.createdAt,
      updatedAt: district.updatedAt,
    };
  }

  async update(id: number, updateDistrictDto: UpdateDistrictDto): Promise<UpdateDistrictResponseDto> {
    const { code, name, provinceId } = updateDistrictDto;

    // Check if district exists
    const existingDistrict = await this.prisma.district.findUnique({
      where: { id },
    });

    if (!existingDistrict) {
      throw new NotFoundException('District not found');
    }

    // Check if province exists if provided
    if (provinceId) {
      const province = await this.prisma.province.findUnique({
        where: { id: provinceId },
      });

      if (!province) {
        throw new NotFoundException('Province not found');
      }
    }

    // Check for conflicts with other districts
    if (code || name) {
      const conflictDistrict = await this.prisma.district.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                code ? { code } : {},
                name ? { name } : {},
              ].filter(Boolean),
            },
          ],
        },
      });

      if (conflictDistrict) {
        if (conflictDistrict.code === code) {
          throw new ConflictException('District with this code already exists');
        }
        if (conflictDistrict.name === name) {
          throw new ConflictException('District with this name already exists');
        }
      }
    }

    // Update district
    const updatedDistrict = await this.prisma.district.update({
      where: { id },
      data: {
        ...(code && { code }),
        ...(name && { name }),
        ...(provinceId && { provinceId }),
      },
    });

    return {
      district: {
        id: updatedDistrict.id,
        code: updatedDistrict.code,
        name: updatedDistrict.name,
        provinceId: updatedDistrict.provinceId,
        updatedAt: updatedDistrict.updatedAt,
      },
      message: 'District updated successfully',
    };
  }

  async remove(id: number): Promise<{ message: string }> {
    // Check if district exists
    const existingDistrict = await this.prisma.district.findUnique({
      where: { id },
      include: {
        _count: {
          select: { sectors: true },
        },
      },
    });

    if (!existingDistrict) {
      throw new NotFoundException('District not found');
    }

    // Check if district has sectors
    if (existingDistrict._count.sectors > 0) {
      throw new BadRequestException(
        `Cannot delete district. ${existingDistrict._count.sectors} sector(s) belong to this district`,
      );
    }

    // Delete district
    await this.prisma.district.delete({
      where: { id },
    });

    return { message: 'District deleted successfully' };
  }
}
