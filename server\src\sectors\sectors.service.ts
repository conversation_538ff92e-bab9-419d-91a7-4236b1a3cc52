import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateSectorDto,
  CreateSectorResponseDto,
} from './dto/create-sector.dto';
import {
  UpdateSectorDto,
  UpdateSectorResponseDto,
} from './dto/update-sector.dto';
import {
  SectorResponseDto,
  SectorsListResponseDto,
} from './dto/sector-response.dto';

@Injectable()
export class SectorsService {
  constructor(private prisma: PrismaService) {}

  async create(createSectorDto: CreateSectorDto): Promise<CreateSectorResponseDto> {
    const { code, name, districtId } = createSectorDto;

    // Check if district exists
    const district = await this.prisma.district.findUnique({
      where: { id: districtId },
    });

    if (!district) {
      throw new NotFoundException('District not found');
    }

    // Check if sector with same code or name already exists
    const existingSector = await this.prisma.sector.findFirst({
      where: {
        OR: [
          { code },
          { name },
        ],
      },
    });

    if (existingSector) {
      if (existingSector.code === code) {
        throw new ConflictException('Sector with this code already exists');
      }
      if (existingSector.name === name) {
        throw new ConflictException('Sector with this name already exists');
      }
    }

    // Create sector
    const sector = await this.prisma.sector.create({
      data: {
        code,
        name,
        districtId,
      },
    });

    return {
      sector: {
        id: sector.id,
        code: sector.code,
        name: sector.name,
        districtId: sector.districtId,
        createdAt: sector.createdAt,
      },
      message: 'Sector created successfully',
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    districtId?: number,
  ): Promise<SectorsListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      AND: [
        search
          ? {
              OR: [
                { name: { contains: search, mode: 'insensitive' as const } },
                { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
              ].filter(Boolean),
            }
          : {},
        districtId ? { districtId } : {},
      ],
    };

    const [sectors, total] = await Promise.all([
      this.prisma.sector.findMany({
        where,
        skip,
        take: limit,
        include: {
          district: {
            include: {
              province: true,
            },
          },
          _count: {
            select: { cells: true },
          },
        },
        orderBy: {
          code: 'asc',
        },
      }),
      this.prisma.sector.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      sectors: sectors.map((sector) => ({
        id: sector.id,
        code: sector.code,
        name: sector.name,
        district: {
          id: sector.district.id,
          name: sector.district.name,
          code: sector.district.code,
          province: {
            id: sector.district.province.id,
            name: sector.district.province.name,
            code: sector.district.province.code,
          },
        },
        cellCount: sector._count.cells,
        createdAt: sector.createdAt,
        updatedAt: sector.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: number): Promise<SectorResponseDto> {
    const sector = await this.prisma.sector.findUnique({
      where: { id },
      include: {
        district: {
          include: {
            province: true,
          },
        },
        _count: {
          select: { cells: true },
        },
      },
    });

    if (!sector) {
      throw new NotFoundException('Sector not found');
    }

    return {
      id: sector.id,
      code: sector.code,
      name: sector.name,
      district: {
        id: sector.district.id,
        name: sector.district.name,
        code: sector.district.code,
        province: {
          id: sector.district.province.id,
          name: sector.district.province.name,
          code: sector.district.province.code,
        },
      },
      cellCount: sector._count.cells,
      createdAt: sector.createdAt,
      updatedAt: sector.updatedAt,
    };
  }

  async update(id: number, updateSectorDto: UpdateSectorDto): Promise<UpdateSectorResponseDto> {
    const { code, name, districtId } = updateSectorDto;

    // Check if sector exists
    const existingSector = await this.prisma.sector.findUnique({
      where: { id },
    });

    if (!existingSector) {
      throw new NotFoundException('Sector not found');
    }

    // Check if district exists if provided
    if (districtId) {
      const district = await this.prisma.district.findUnique({
        where: { id: districtId },
      });

      if (!district) {
        throw new NotFoundException('District not found');
      }
    }

    // Check for conflicts with other sectors
    if (code || name) {
      const conflictSector = await this.prisma.sector.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                code ? { code } : {},
                name ? { name } : {},
              ].filter(Boolean),
            },
          ],
        },
      });

      if (conflictSector) {
        if (conflictSector.code === code) {
          throw new ConflictException('Sector with this code already exists');
        }
        if (conflictSector.name === name) {
          throw new ConflictException('Sector with this name already exists');
        }
      }
    }

    // Update sector
    const updatedSector = await this.prisma.sector.update({
      where: { id },
      data: {
        ...(code && { code }),
        ...(name && { name }),
        ...(districtId && { districtId }),
      },
    });

    return {
      sector: {
        id: updatedSector.id,
        code: updatedSector.code,
        name: updatedSector.name,
        districtId: updatedSector.districtId,
        updatedAt: updatedSector.updatedAt,
      },
      message: 'Sector updated successfully',
    };
  }

  async remove(id: number): Promise<{ message: string }> {
    // Check if sector exists
    const existingSector = await this.prisma.sector.findUnique({
      where: { id },
      include: {
        _count: {
          select: { cells: true },
        },
      },
    });

    if (!existingSector) {
      throw new NotFoundException('Sector not found');
    }

    // Check if sector has cells
    if (existingSector._count.cells > 0) {
      throw new BadRequestException(
        `Cannot delete sector. ${existingSector._count.cells} cell(s) belong to this sector`,
      );
    }

    // Delete sector
    await this.prisma.sector.delete({
      where: { id },
    });

    return { message: 'Sector deleted successfully' };
  }
}
