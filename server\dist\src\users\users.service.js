"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const email_service_1 = require("../email/email.service");
const uuid_1 = require("uuid");
let UsersService = class UsersService {
    prisma;
    emailService;
    constructor(prisma, emailService) {
        this.prisma = prisma;
        this.emailService = emailService;
    }
    async create(createUserDto) {
        const { firstName, lastName, email, telephoneNumber, roleId, locations } = createUserDto;
        const existingUser = await this.prisma.user.findFirst({
            where: {
                OR: [
                    { email },
                    { telephoneNumber },
                ],
            },
        });
        if (existingUser) {
            if (existingUser.email === email) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            if (existingUser.telephoneNumber === telephoneNumber) {
                throw new common_1.ConflictException('User with this telephone number already exists');
            }
        }
        const role = await this.prisma.role.findUnique({
            where: { id: roleId },
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        if (locations && locations.length > 0) {
            await this.validateLocationAccess(locations);
        }
        const verificationToken = (0, uuid_1.v4)();
        const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
        const result = await this.prisma.$transaction(async (tx) => {
            const user = await tx.user.create({
                data: {
                    firstName,
                    lastName,
                    email,
                    telephoneNumber,
                    roleId,
                },
            });
            await tx.account.create({
                data: {
                    userId: user.id,
                    resetToken: verificationToken,
                    resetTokenExpiry: tokenExpiry,
                },
            });
            if (locations && locations.length > 0) {
                await tx.userLocationAccess.createMany({
                    data: locations.map((location) => ({
                        userId: user.id,
                        ...location,
                    })),
                });
            }
            return user;
        });
        await this.emailService.sendUserCreationEmail(email, firstName, verificationToken);
        const createdUser = await this.prisma.user.findUnique({
            where: { id: result.id },
            include: {
                role: true,
            },
        });
        return {
            user: {
                id: createdUser.id,
                firstName: createdUser.firstName,
                lastName: createdUser.lastName,
                email: createdUser.email,
                telephoneNumber: createdUser.telephoneNumber,
                role: {
                    name: createdUser.role.name,
                    privileges: createdUser.role.privileges,
                },
                createdAt: createdUser.createdAt,
            },
            message: 'User created successfully. Setup email sent.',
        };
    }
    async findAll(page = 1, limit = 10, search, roleId) {
        const skip = (page - 1) * limit;
        const where = {
            AND: [
                search
                    ? {
                        OR: [
                            { firstName: { contains: search, mode: 'insensitive' } },
                            { lastName: { contains: search, mode: 'insensitive' } },
                            { email: { contains: search, mode: 'insensitive' } },
                        ],
                    }
                    : {},
                roleId ? { roleId } : {},
            ],
        };
        const [users, total] = await Promise.all([
            this.prisma.user.findMany({
                where,
                skip,
                take: limit,
                include: {
                    role: true,
                    account: {
                        select: {
                            accountVerified: true,
                            is2FAEnabled: true,
                        },
                    },
                    locations: {
                        include: {
                            province: true,
                            district: true,
                            sector: true,
                            cell: true,
                            village: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.user.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            users: users.map((user) => ({
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                telephoneNumber: user.telephoneNumber,
                role: {
                    id: user.role.id,
                    name: user.role.name,
                    privileges: user.role.privileges,
                },
                accountVerified: user.account?.accountVerified || false,
                is2FAEnabled: user.account?.is2FAEnabled || false,
                locations: user.locations.map((location) => ({
                    id: location.id,
                    province: location.province,
                    district: location.district,
                    sector: location.sector,
                    cell: location.cell,
                    village: location.village,
                })),
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            })),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            include: {
                role: true,
                account: {
                    select: {
                        accountVerified: true,
                        is2FAEnabled: true,
                    },
                },
                locations: {
                    include: {
                        province: true,
                        district: true,
                        sector: true,
                        cell: true,
                        village: true,
                    },
                },
            },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            telephoneNumber: user.telephoneNumber,
            role: {
                id: user.role.id,
                name: user.role.name,
                privileges: user.role.privileges,
            },
            accountVerified: user.account?.accountVerified || false,
            is2FAEnabled: user.account?.is2FAEnabled || false,
            locations: user.locations.map((location) => ({
                id: location.id,
                province: location.province,
                district: location.district,
                sector: location.sector,
                cell: location.cell,
                village: location.village,
            })),
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
        };
    }
    async update(id, updateUserDto) {
        const { firstName, lastName, email, telephoneNumber, roleId, locations } = updateUserDto;
        const existingUser = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!existingUser) {
            throw new common_1.NotFoundException('User not found');
        }
        if (email || telephoneNumber) {
            const conflictUser = await this.prisma.user.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        {
                            OR: [
                                email ? { email } : {},
                                telephoneNumber ? { telephoneNumber } : {},
                            ].filter(Boolean),
                        },
                    ],
                },
            });
            if (conflictUser) {
                if (conflictUser.email === email) {
                    throw new common_1.ConflictException('User with this email already exists');
                }
                if (conflictUser.telephoneNumber === telephoneNumber) {
                    throw new common_1.ConflictException('User with this telephone number already exists');
                }
            }
        }
        if (roleId) {
            const role = await this.prisma.role.findUnique({
                where: { id: roleId },
            });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
        }
        if (locations && locations.length > 0) {
            await this.validateLocationAccess(locations);
        }
        const updatedUser = await this.prisma.$transaction(async (tx) => {
            const user = await tx.user.update({
                where: { id },
                data: {
                    ...(firstName && { firstName }),
                    ...(lastName && { lastName }),
                    ...(email && { email }),
                    ...(telephoneNumber && { telephoneNumber }),
                    ...(roleId && { roleId }),
                },
            });
            if (locations !== undefined) {
                await tx.userLocationAccess.deleteMany({
                    where: { userId: id },
                });
                if (locations.length > 0) {
                    await tx.userLocationAccess.createMany({
                        data: locations.map((location) => ({
                            userId: id,
                            ...location,
                        })),
                    });
                }
            }
            return user;
        });
        const userWithRole = await this.prisma.user.findUnique({
            where: { id },
            include: {
                role: true,
            },
        });
        return {
            user: {
                id: userWithRole.id,
                firstName: userWithRole.firstName,
                lastName: userWithRole.lastName,
                email: userWithRole.email,
                telephoneNumber: userWithRole.telephoneNumber,
                role: {
                    name: userWithRole.role.name,
                    privileges: userWithRole.role.privileges,
                },
                updatedAt: userWithRole.updatedAt,
            },
            message: 'User updated successfully',
        };
    }
    async remove(id) {
        const existingUser = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!existingUser) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.recoveryCode.deleteMany({
                where: { accountId: id },
            });
            await tx.account.deleteMany({
                where: { userId: id },
            });
            await tx.userLocationAccess.deleteMany({
                where: { userId: id },
            });
            await tx.user.delete({
                where: { id },
            });
        });
        return { message: 'User deleted successfully' };
    }
    async resendVerificationEmail(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            include: { account: true },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (!user.account) {
            throw new common_1.BadRequestException('User account not found');
        }
        if (user.account.accountVerified) {
            throw new common_1.BadRequestException('Account is already verified');
        }
        const verificationToken = (0, uuid_1.v4)();
        const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
        await this.prisma.account.update({
            where: { userId: id },
            data: {
                resetToken: verificationToken,
                resetTokenExpiry: tokenExpiry,
            },
        });
        await this.emailService.sendUserCreationEmail(user.email, user.firstName, verificationToken);
        return { message: 'Verification email sent successfully' };
    }
    async validateLocationAccess(locations) {
        for (const location of locations) {
            if (location.provinceId) {
                const province = await this.prisma.province.findUnique({
                    where: { id: location.provinceId },
                });
                if (!province) {
                    throw new common_1.NotFoundException(`Province with ID ${location.provinceId} not found`);
                }
            }
            if (location.districtId) {
                const district = await this.prisma.district.findUnique({
                    where: { id: location.districtId },
                });
                if (!district) {
                    throw new common_1.NotFoundException(`District with ID ${location.districtId} not found`);
                }
                if (location.provinceId && district.provinceId !== location.provinceId) {
                    throw new common_1.BadRequestException(`District ${location.districtId} does not belong to province ${location.provinceId}`);
                }
            }
            if (location.sectorId) {
                const sector = await this.prisma.sector.findUnique({
                    where: { id: location.sectorId },
                });
                if (!sector) {
                    throw new common_1.NotFoundException(`Sector with ID ${location.sectorId} not found`);
                }
                if (location.districtId && sector.districtId !== location.districtId) {
                    throw new common_1.BadRequestException(`Sector ${location.sectorId} does not belong to district ${location.districtId}`);
                }
            }
            if (location.cellId) {
                const cell = await this.prisma.cell.findUnique({
                    where: { id: location.cellId },
                });
                if (!cell) {
                    throw new common_1.NotFoundException(`Cell with ID ${location.cellId} not found`);
                }
                if (location.sectorId && cell.sectorId !== location.sectorId) {
                    throw new common_1.BadRequestException(`Cell ${location.cellId} does not belong to sector ${location.sectorId}`);
                }
            }
            if (location.villageId) {
                const village = await this.prisma.village.findUnique({
                    where: { id: location.villageId },
                });
                if (!village) {
                    throw new common_1.NotFoundException(`Village with ID ${location.villageId} not found`);
                }
                if (location.cellId && village.cellId !== location.cellId) {
                    throw new common_1.BadRequestException(`Village ${location.villageId} does not belong to cell ${location.cellId}`);
                }
            }
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        email_service_1.EmailService])
], UsersService);
//# sourceMappingURL=users.service.js.map