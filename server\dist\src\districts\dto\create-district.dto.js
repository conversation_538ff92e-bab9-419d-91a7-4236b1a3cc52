"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDistrictResponseDto = exports.CreateDistrictDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateDistrictDto {
    code;
    name;
    provinceId;
}
exports.CreateDistrictDto = CreateDistrictDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District code (unique identifier)',
        example: 101,
        minimum: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateDistrictDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District name',
        example: 'Nyarugenge',
        minLength: 2,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    __metadata("design:type", String)
], CreateDistrictDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Province ID this district belongs to',
        example: 1,
        minimum: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateDistrictDto.prototype, "provinceId", void 0);
class CreateDistrictResponseDto {
    district;
    message;
}
exports.CreateDistrictResponseDto = CreateDistrictResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created district information',
    }),
    __metadata("design:type", Object)
], CreateDistrictResponseDto.prototype, "district", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'District created successfully',
    }),
    __metadata("design:type", String)
], CreateDistrictResponseDto.prototype, "message", void 0);
//# sourceMappingURL=create-district.dto.js.map