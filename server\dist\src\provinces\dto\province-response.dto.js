"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProvincesListResponseDto = exports.ProvinceResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ProvinceResponseDto {
    id;
    code;
    name;
    districtCount;
    createdAt;
    updatedAt;
}
exports.ProvinceResponseDto = ProvinceResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Province ID',
        example: 1,
    }),
    __metadata("design:type", Number)
], ProvinceResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Province code',
        example: 1,
    }),
    __metadata("design:type", Number)
], ProvinceResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Province name',
        example: 'Kigali City',
    }),
    __metadata("design:type", String)
], ProvinceResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of districts in this province',
        example: 3,
    }),
    __metadata("design:type", Number)
], ProvinceResponseDto.prototype, "districtCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Province creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ProvinceResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Province last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ProvinceResponseDto.prototype, "updatedAt", void 0);
class ProvincesListResponseDto {
    provinces;
    total;
    page;
    limit;
    totalPages;
}
exports.ProvincesListResponseDto = ProvincesListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of provinces',
        type: [ProvinceResponseDto],
    }),
    __metadata("design:type", Array)
], ProvincesListResponseDto.prototype, "provinces", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of provinces',
        example: 5,
    }),
    __metadata("design:type", Number)
], ProvincesListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], ProvincesListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], ProvincesListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 1,
    }),
    __metadata("design:type", Number)
], ProvincesListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=province-response.dto.js.map