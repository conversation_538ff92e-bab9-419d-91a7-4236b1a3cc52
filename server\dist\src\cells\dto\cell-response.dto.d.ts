export declare class CellResponseDto {
    id: number;
    code: number;
    name: string;
    sector: {
        id: number;
        name: string;
        code: number;
        district: {
            id: number;
            name: string;
            code: number;
            province: {
                id: number;
                name: string;
                code: number;
            };
        };
    };
    villageCount: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare class CellsListResponseDto {
    cells: CellResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
