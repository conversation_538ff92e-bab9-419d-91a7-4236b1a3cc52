"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplateService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const Handlebars = __importStar(require("handlebars"));
const fs_1 = require("fs");
const path_1 = require("path");
let TemplateService = TemplateService_1 = class TemplateService {
    configService;
    logger = new common_1.Logger(TemplateService_1.name);
    templatesDir;
    compiledTemplates = new Map();
    constructor(configService) {
        this.configService = configService;
        this.templatesDir = (0, path_1.join)(__dirname, 'templates');
        this.registerHelpers();
    }
    registerHelpers() {
    }
    getTemplate(templateName) {
        if (this.compiledTemplates.has(templateName)) {
            return this.compiledTemplates.get(templateName);
        }
        try {
            const templatePath = (0, path_1.join)(this.templatesDir, `${templateName}.hbs`);
            const templateContent = (0, fs_1.readFileSync)(templatePath, 'utf-8');
            const compiledTemplate = Handlebars.compile(templateContent);
            this.compiledTemplates.set(templateName, compiledTemplate);
            return compiledTemplate;
        }
        catch (error) {
            this.logger.error(`Failed to load template ${templateName}:`, error);
            throw new Error(`Template ${templateName} not found or could not be compiled`);
        }
    }
    getBaseContext() {
        return {
            year: new Date().getFullYear(),
            logoUrl: this.configService.get('LOGO_URL'),
            supportEmail: this.configService.get('SUPPORT_EMAIL'),
            supportPhone: this.configService.get('SUPPORT_PHONE'),
            frontendUrl: this.configService.get('FRONTEND_URL'),
            socialLinks: {
                twitter: this.configService.get('TWITTER_URL'),
                facebook: this.configService.get('FACEBOOK_URL'),
                linkedin: this.configService.get('LINKEDIN_URL')
            },
            unsubscribeUrl: `${this.configService.get('FRONTEND_URL')}`,
            preferencesUrl: `${this.configService.get('FRONTEND_URL')}`
        };
    }
    async renderTemplate(templateName, context) {
        try {
            const template = this.getTemplate(templateName);
            const fullContext = {
                ...this.getBaseContext(),
                ...context
            };
            const renderedHtml = template(fullContext);
            this.logger.debug(`Template ${templateName} rendered successfully`);
            return renderedHtml;
        }
        catch (error) {
            this.logger.error(`Failed to render template ${templateName}:`, error);
            throw error;
        }
    }
    clearTemplateCache() {
        this.compiledTemplates.clear();
        this.logger.log('Template cache cleared');
    }
};
exports.TemplateService = TemplateService;
exports.TemplateService = TemplateService = TemplateService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], TemplateService);
//# sourceMappingURL=template.service.js.map