import { CreateDistrictDto } from './create-district.dto';
declare const UpdateDistrictDto_base: import("@nestjs/common").Type<Partial<CreateDistrictDto>>;
export declare class UpdateDistrictDto extends UpdateDistrictDto_base {
}
export declare class UpdateDistrictResponseDto {
    district: {
        id: number;
        code: number;
        name: string;
        provinceId: number;
        updatedAt: Date;
    };
    message: string;
}
export {};
