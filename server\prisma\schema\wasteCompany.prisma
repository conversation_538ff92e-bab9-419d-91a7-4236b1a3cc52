model WasteCollectionCompany {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    companyName  String
    ownerName    String
    ownerGender  Gender
    contactPhone String
    contactEmail String

    companyType        ServiceProviderType
    totalPersonnel     Int
    femalePersonnel    Int
    malePersonnel      Int
    clientTypes        ClientType[]
    wasteSeparation    Boolean
    separatedMaterials WasteMaterial[]
    wasteDestination   WasteDestination
    destinationDetails String?
    weighbridge        Boolean
    recordingMethod    RecordingMethod?
}

model WasteRecoveryCompany {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    companyName      String
    contactPerson    String
    contactPhone     String
    contactEmail     String
    companyType      ServiceProviderType
    totalPersonnel   Int
    femalePersonnel  Int
    malePersonnel    Int
    operationType    OperationType
    handledMaterials String[]
    businessSites    String[]
}

model WasteDisposalCompany {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    companyName         String
    facilityLocation    String
    contactPerson       String
    contactPhone        String
    contactEmail        String
    companyType         ServiceProviderType
    totalPersonnel      Int
    femalePersonnel     Int
    malePersonnel       Int
    clientTypes         ClientType[]
    boundaryControl     Boolean
    wasteDepositControl Boolean
    compactionFrequency CompactionFrequency
    wasteBurning        Boolean
    weighbridge         Boolean
    wasteAmount         String
    truckFrequency      TruckFrequency
    recordingMethod     RecordingMethod?
}
