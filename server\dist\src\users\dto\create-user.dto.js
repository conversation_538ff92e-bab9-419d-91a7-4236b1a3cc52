"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserResponseDto = exports.CreateUserDto = exports.LocationAccessDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class LocationAccessDto {
    provinceId;
    districtId;
    sectorId;
    cellId;
    villageId;
}
exports.LocationAccessDto = LocationAccessDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Province ID',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], LocationAccessDto.prototype, "provinceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District ID',
        example: 101,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], LocationAccessDto.prototype, "districtId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector ID',
        example: 10101,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], LocationAccessDto.prototype, "sectorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell ID',
        example: 1010101,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], LocationAccessDto.prototype, "cellId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Village ID',
        example: 101010101,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], LocationAccessDto.prototype, "villageId", void 0);
class CreateUserDto {
    firstName;
    lastName;
    email;
    telephoneNumber;
    roleId;
    locations;
}
exports.CreateUserDto = CreateUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User first name',
        example: 'John',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    __metadata("design:type", String)
], CreateUserDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User last name',
        example: 'Doe',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    __metadata("design:type", String)
], CreateUserDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User email address',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User telephone number',
        example: '+250788123456',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "telephoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role ID to assign to the user',
        example: 'role_123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "roleId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location access configuration',
        required: false,
        type: [LocationAccessDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => LocationAccessDto),
    __metadata("design:type", Array)
], CreateUserDto.prototype, "locations", void 0);
class CreateUserResponseDto {
    user;
    message;
}
exports.CreateUserResponseDto = CreateUserResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created user information',
    }),
    __metadata("design:type", Object)
], CreateUserResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'User created successfully. Setup email sent.',
    }),
    __metadata("design:type", String)
], CreateUserResponseDto.prototype, "message", void 0);
//# sourceMappingURL=create-user.dto.js.map