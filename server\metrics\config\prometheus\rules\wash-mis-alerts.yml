groups:
  - name: wash-mis-application
    rules:
      # High HTTP error rate
      - alert: HighHTTPErrorRate
        expr: rate(wash_mis_http_requests_total{status_code=~"5.."}[5m]) / rate(wash_mis_http_requests_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          service: wash-mis-api
        annotations:
          summary: "High HTTP error rate detected"
          description: "HTTP error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # High response time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(wash_mis_http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: wash-mis-api
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      # High memory usage
      - alert: HighMemoryUsage
        expr: wash_mis_process_resident_memory_bytes / 1024 / 1024 > 512
        for: 5m
        labels:
          severity: warning
          service: wash-mis-api
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}MB"

      # Application down
      - alert: ApplicationDown
        expr: up{job="wash-mis-api"} == 0
        for: 1m
        labels:
          severity: critical
          service: wash-mis-api
        annotations:
          summary: "WASH MIS API is down"
          description: "The WASH MIS API has been down for more than 1 minute"

      # High authentication failure rate
      - alert: HighAuthFailureRate
        expr: rate(wash_mis_auth_attempts_total{status="failure"}[5m]) / rate(wash_mis_auth_attempts_total[5m]) > 0.3
        for: 3m
        labels:
          severity: warning
          service: wash-mis-api
        annotations:
          summary: "High authentication failure rate"
          description: "Authentication failure rate is {{ $value | humanizePercentage }}"

      # Database query slow
      - alert: SlowDatabaseQueries
        expr: histogram_quantile(0.95, rate(wash_mis_database_query_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: wash-mis-api
        annotations:
          summary: "Slow database queries detected"
          description: "95th percentile database query time is {{ $value }}s"

      # High error rate
      - alert: HighApplicationErrorRate
        expr: rate(wash_mis_errors_total[5m]) > 10
        for: 2m
        labels:
          severity: critical
          service: wash-mis-api
        annotations:
          summary: "High application error rate"
          description: "Application error rate is {{ $value }} errors per second"

  - name: wash-mis-business
    rules:
      # Low data submission rate
      - alert: LowDataSubmissionRate
        expr: rate(wash_mis_data_submissions_total{status="success"}[1h]) < 0.1
        for: 30m
        labels:
          severity: warning
          service: wash-mis-api
        annotations:
          summary: "Low data submission rate"
          description: "Data submission rate is {{ $value }} submissions per second over the last hour"

      # High data submission failure rate
      - alert: HighDataSubmissionFailureRate
        expr: rate(wash_mis_data_submissions_total{status="failure"}[5m]) / rate(wash_mis_data_submissions_total[5m]) > 0.2
        for: 10m
        labels:
          severity: warning
          service: wash-mis-api
        annotations:
          summary: "High data submission failure rate"
          description: "Data submission failure rate is {{ $value | humanizePercentage }}"

      # Email sending failures
      - alert: EmailSendingFailures
        expr: rate(wash_mis_emails_sent_total{status="failure"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: wash-mis-api
        annotations:
          summary: "Email sending failures detected"
          description: "Email failure rate is {{ $value }} failures per second"

  - name: wash-mis-infrastructure
    rules:
      # Mimir down
      - alert: MimirDown
        expr: up{job="mimir"} == 0
        for: 1m
        labels:
          severity: critical
          service: mimir
        annotations:
          summary: "Mimir is down"
          description: "Mimir has been down for more than 1 minute"

      # Loki down
      - alert: LokiDown
        expr: up{job="loki"} == 0
        for: 1m
        labels:
          severity: critical
          service: loki
        annotations:
          summary: "Loki is down"
          description: "Loki has been down for more than 1 minute"

      # Tempo down
      - alert: TempoDown
        expr: up{job="tempo"} == 0
        for: 1m
        labels:
          severity: critical
          service: tempo
        annotations:
          summary: "Tempo is down"
          description: "Tempo has been down for more than 1 minute"

      # Grafana down
      - alert: GrafanaDown
        expr: up{job="grafana"} == 0
        for: 1m
        labels:
          severity: warning
          service: grafana
        annotations:
          summary: "Grafana is down"
          description: "Grafana has been down for more than 1 minute"

      # High disk usage (if available)
      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.85
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.device }}"
