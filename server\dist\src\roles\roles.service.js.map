{"version": 3, "file": "roles.service.js", "sourceRoot": "", "sources": ["../../../src/roles/roles.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,6DAAyD;AAelD,IAAM,YAAY,GAAlB,MAAM,YAAY;IACH;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,MAAM,CAAC,aAA4B;QACvC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;QAGjD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACpD,KAAK,EAAE;gBACL,EAAE,EAAE;oBACF,EAAE,IAAI,EAAE;oBACR,EAAE,IAAI,EAAE;iBACT;aACF;SACF,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,YAAY,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAC/B,MAAM,IAAI,0BAAiB,CAAC,oCAAoC,CAAC,CAAC;YACpE,CAAC;YACD,IAAI,YAAY,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAC/B,MAAM,IAAI,0BAAiB,CAAC,oCAAoC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAGD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACzC,IAAI,EAAE;gBACJ,IAAI;gBACJ,IAAI;gBACJ,UAAU;aACX;SACF,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B;YACD,OAAO,EAAE,2BAA2B;SACrC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,CACX,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,MAAe;QAEf,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAEhC,MAAM,KAAK,GAAG,MAAM;YAClB,CAAC,CAAC;gBACE,EAAE,EAAE;oBACF,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAsB,EAAE,EAAE;oBAC5D,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAsB,EAAE,EAAE;iBAC7D;aACF;YACH,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACxB,KAAK;gBACL,IAAI;gBACJ,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;qBACxB;iBACF;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;aACF,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;SAClC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAC1B,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBAC5B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAC,CAAC;YACH,KAAK;YACL,IAAI;YACJ,KAAK;YACL,UAAU;SACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;iBACxB;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC;QAED,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YAC5B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,aAA4B;QACnD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;QAGjD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC;QAGD,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACpD,KAAK,EAAE;oBACL,GAAG,EAAE;wBACH,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE;wBACnB;4BACE,EAAE,EAAE;gCACF,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;gCACpB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;6BACrB,CAAC,MAAM,CAAC,OAAO,CAAC;yBAClB;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,YAAY,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;oBAC/B,MAAM,IAAI,0BAAiB,CAAC,oCAAoC,CAAC,CAAC;gBACpE,CAAC;gBACD,IAAI,YAAY,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;oBAC/B,MAAM,IAAI,0BAAiB,CAAC,oCAAoC,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;QACH,CAAC;QAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAChD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE;gBACJ,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC;gBACrB,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC;gBACrB,GAAG,CAAC,UAAU,IAAI,EAAE,UAAU,EAAE,CAAC;aAClC;SACF,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE;gBACJ,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC;YACD,OAAO,EAAE,2BAA2B;SACrC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QAErB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;iBACxB;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC;QAGD,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,4BAAmB,CAC3B,uBAAuB,YAAY,CAAC,MAAM,CAAC,KAAK,oCAAoC,CACrF,CAAC;QACJ,CAAC;QAGD,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;IAClD,CAAC;CACF,CAAA;AApNY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,YAAY,CAoNxB"}