"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CellsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const cells_service_1 = require("./cells.service");
const auth_guard_1 = require("../common/guards/auth.guard");
const privilege_guard_1 = require("../common/guards/privilege.guard");
const mobile_guard_1 = require("../common/guards/mobile.guard");
const privileges_decorator_1 = require("../common/decorators/privileges.decorator");
const client_1 = require("@prisma/client");
const create_cell_dto_1 = require("./dto/create-cell.dto");
const update_cell_dto_1 = require("./dto/update-cell.dto");
const cell_response_dto_1 = require("./dto/cell-response.dto");
let CellsController = class CellsController {
    cellsService;
    constructor(cellsService) {
        this.cellsService = cellsService;
    }
    async create(createCellDto) {
        return this.cellsService.create(createCellDto);
    }
    async findAll(page, limit, search, sectorId) {
        return this.cellsService.findAll(page, limit, search, sectorId);
    }
    async findOne(id) {
        return this.cellsService.findOne(id);
    }
    async update(id, updateCellDto) {
        return this.cellsService.update(id, updateCellDto);
    }
    async remove(id) {
        return this.cellsService.remove(id);
    }
};
exports.CellsController = CellsController;
__decorate([
    (0, common_1.Post)(),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new cell' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Cell created successfully',
        type: create_cell_dto_1.CreateCellResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Sector not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Cell already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cell_dto_1.CreateCellDto]),
    __metadata("design:returntype", Promise)
], CellsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Get all cells with pagination and filtering' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search by cell name or code',
        example: 'Gisozi',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sectorId',
        required: false,
        type: Number,
        description: 'Filter by sector ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cells retrieved successfully',
        type: cell_response_dto_1.CellsListResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Query)('page', new common_1.ParseIntPipe({ optional: true }))),
    __param(1, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('sectorId', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, Number]),
    __metadata("design:returntype", Promise)
], CellsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Get a cell by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Cell ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cell retrieved successfully',
        type: cell_response_dto_1.CellResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cell not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CellsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Update a cell' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Cell ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cell updated successfully',
        type: update_cell_dto_1.UpdateCellResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cell or Sector not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Cell already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_cell_dto_1.UpdateCellDto]),
    __metadata("design:returntype", Promise)
], CellsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a cell' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Cell ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cell deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Cell deleted successfully',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Cell has villages' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cell not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CellsController.prototype, "remove", null);
exports.CellsController = CellsController = __decorate([
    (0, swagger_1.ApiTags)('Cells'),
    (0, common_1.Controller)('cells'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, mobile_guard_1.MobileGuard, privilege_guard_1.PrivilegeGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [cells_service_1.CellsService])
], CellsController);
//# sourceMappingURL=cells.controller.js.map