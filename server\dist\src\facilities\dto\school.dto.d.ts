import { BaseFacilityWithNameDto, BaseFacilityWithNameResponseDto, BasePaginatedResponseDto, BaseCreateResponseDto, BaseUpdateResponseDto } from './base-facility.dto';
export declare class CreateSchoolDto extends BaseFacilityWithNameDto {
}
declare const UpdateSchoolDto_base: import("@nestjs/common").Type<Partial<CreateSchoolDto>>;
export declare class UpdateSchoolDto extends UpdateSchoolDto_base {
}
export declare class SchoolResponseDto extends BaseFacilityWithNameResponseDto {
}
export declare class SchoolsListResponseDto extends BasePaginatedResponseDto<SchoolResponseDto> {
    data: SchoolResponseDto[];
}
export declare class CreateSchoolResponseDto extends BaseCreateResponseDto<SchoolResponseDto> {
    facility: SchoolResponseDto;
    message: string;
}
export declare class UpdateSchoolResponseDto extends BaseUpdateResponseDto<SchoolResponseDto> {
    facility: SchoolResponseDto;
    message: string;
}
export {};
