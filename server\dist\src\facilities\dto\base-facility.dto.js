"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeleteResponseDto = exports.BaseUpdateResponseDto = exports.BaseCreateResponseDto = exports.BasePaginatedResponseDto = exports.BaseFacilityWithNameResponseDto = exports.BaseFacilityResponseDto = exports.PaginationQueryDto = exports.BaseFacilityWithNameDto = exports.BaseFacilityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class BaseFacilityDto {
    locationId;
    number;
}
exports.BaseFacilityDto = BaseFacilityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location ID where the facility is located',
        example: 'location_123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BaseFacilityDto.prototype, "locationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility number (unique identifier)',
        example: 1001,
        minimum: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], BaseFacilityDto.prototype, "number", void 0);
class BaseFacilityWithNameDto extends BaseFacilityDto {
    name;
}
exports.BaseFacilityWithNameDto = BaseFacilityWithNameDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility name',
        example: 'Central Health Center',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BaseFacilityWithNameDto.prototype, "name", void 0);
class PaginationQueryDto {
    page = 1;
    limit = 10;
    search;
}
exports.PaginationQueryDto = PaginationQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Page number',
        example: 1,
        required: false,
        minimum: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === undefined || value === null || value === '')
            return 1;
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? 1 : parsed;
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PaginationQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
        required: false,
        minimum: 1,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === undefined || value === null || value === '')
            return 10;
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? 10 : parsed;
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PaginationQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Search term for filtering facilities',
        example: 'health center',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PaginationQueryDto.prototype, "search", void 0);
class BaseFacilityResponseDto {
    id;
    number;
    location;
    createdAt;
    updatedAt;
}
exports.BaseFacilityResponseDto = BaseFacilityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility ID',
        example: 'facility_123',
    }),
    __metadata("design:type", String)
], BaseFacilityResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility number',
        example: 1001,
    }),
    __metadata("design:type", Number)
], BaseFacilityResponseDto.prototype, "number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location information',
    }),
    __metadata("design:type", Object)
], BaseFacilityResponseDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], BaseFacilityResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], BaseFacilityResponseDto.prototype, "updatedAt", void 0);
class BaseFacilityWithNameResponseDto extends BaseFacilityResponseDto {
    name;
}
exports.BaseFacilityWithNameResponseDto = BaseFacilityWithNameResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility name',
        example: 'Central Health Center',
    }),
    __metadata("design:type", String)
], BaseFacilityWithNameResponseDto.prototype, "name", void 0);
class BasePaginatedResponseDto {
    total;
    page;
    limit;
    totalPages;
    data;
}
exports.BasePaginatedResponseDto = BasePaginatedResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of items',
        example: 100,
    }),
    __metadata("design:type", Number)
], BasePaginatedResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], BasePaginatedResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], BasePaginatedResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 10,
    }),
    __metadata("design:type", Number)
], BasePaginatedResponseDto.prototype, "totalPages", void 0);
class BaseCreateResponseDto {
    message;
    facility;
}
exports.BaseCreateResponseDto = BaseCreateResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Facility created successfully',
    }),
    __metadata("design:type", String)
], BaseCreateResponseDto.prototype, "message", void 0);
class BaseUpdateResponseDto {
    message;
    facility;
}
exports.BaseUpdateResponseDto = BaseUpdateResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Facility updated successfully',
    }),
    __metadata("design:type", String)
], BaseUpdateResponseDto.prototype, "message", void 0);
class BaseDeleteResponseDto {
    message;
}
exports.BaseDeleteResponseDto = BaseDeleteResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Facility deleted successfully',
    }),
    __metadata("design:type", String)
], BaseDeleteResponseDto.prototype, "message", void 0);
//# sourceMappingURL=base-facility.dto.js.map