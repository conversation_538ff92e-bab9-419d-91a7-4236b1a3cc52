import { AuthService } from './auth.service';
import { LoginDto, LoginWith2FADto, LoginResponseDto, TwoFactorRequiredDto } from './dto/login.dto';
import { RequestPasswordResetDto, ResetPasswordDto, SetInitialPasswordDto } from './dto/password-reset.dto';
import { Setup2FADto, Verify2FADto, UseRecoveryCodeDto, Setup2FAResponseDto, Enable2FAResponseDto } from './dto/two-factor.dto';
import { RefreshTokenDto, RefreshTokenResponseDto } from './dto/refresh-token.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<LoginResponseDto | TwoFactorRequiredDto>;
    loginWith2FA(loginWith2FADto: LoginWith2FADto): Promise<LoginResponseDto>;
    verify2FA(verify2FADto: Verify2FADto): Promise<LoginResponseDto>;
    useRecoveryCode(useRecoveryCodeDto: UseRecoveryCodeDto): Promise<LoginResponseDto>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<RefreshTokenResponseDto>;
    logout(user: any): Promise<{
        message: string;
    }>;
    requestPasswordReset(requestPasswordResetDto: RequestPasswordResetDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{
        message: string;
    }>;
    setInitialPassword(setInitialPasswordDto: SetInitialPasswordDto): Promise<{
        message: string;
    }>;
    setup2FA(user: any): Promise<Setup2FAResponseDto>;
    enable2FA(user: any, setup2FADto: Setup2FADto): Promise<Enable2FAResponseDto>;
    disable2FA(user: any, totpCode: string): Promise<{
        message: string;
    }>;
    regenerateRecoveryCodes(user: any, totpCode: string): Promise<{
        recoveryCodes: string[];
    }>;
}
