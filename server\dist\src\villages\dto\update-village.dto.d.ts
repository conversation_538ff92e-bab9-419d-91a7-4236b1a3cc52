import { CreateVillageDto } from './create-village.dto';
declare const UpdateVillageDto_base: import("@nestjs/common").Type<Partial<CreateVillageDto>>;
export declare class UpdateVillageDto extends UpdateVillageDto_base {
}
export declare class UpdateVillageResponseDto {
    village: {
        id: number;
        code: number;
        name: string;
        cellId: number;
        updatedAt: Date;
    };
    message: string;
}
export {};
