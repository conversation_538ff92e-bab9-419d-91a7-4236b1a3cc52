"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SectorsListResponseDto = exports.SectorResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class SectorResponseDto {
    id;
    code;
    name;
    district;
    cellCount;
    createdAt;
    updatedAt;
}
exports.SectorResponseDto = SectorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector ID',
        example: 1,
    }),
    __metadata("design:type", Number)
], SectorResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector code',
        example: 10101,
    }),
    __metadata("design:type", Number)
], SectorResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector name',
        example: 'Gitega',
    }),
    __metadata("design:type", String)
], SectorResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District information',
    }),
    __metadata("design:type", Object)
], SectorResponseDto.prototype, "district", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of cells in this sector',
        example: 5,
    }),
    __metadata("design:type", Number)
], SectorResponseDto.prototype, "cellCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], SectorResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], SectorResponseDto.prototype, "updatedAt", void 0);
class SectorsListResponseDto {
    sectors;
    total;
    page;
    limit;
    totalPages;
}
exports.SectorsListResponseDto = SectorsListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of sectors',
        type: [SectorResponseDto],
    }),
    __metadata("design:type", Array)
], SectorsListResponseDto.prototype, "sectors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of sectors',
        example: 416,
    }),
    __metadata("design:type", Number)
], SectorsListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], SectorsListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], SectorsListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 42,
    }),
    __metadata("design:type", Number)
], SectorsListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=sector-response.dto.js.map