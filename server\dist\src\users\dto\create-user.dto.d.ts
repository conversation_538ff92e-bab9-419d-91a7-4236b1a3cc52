export declare class LocationAccessDto {
    provinceId?: number;
    districtId?: number;
    sectorId?: number;
    cellId?: number;
    villageId?: number;
}
export declare class CreateUserDto {
    firstName: string;
    lastName: string;
    email: string;
    telephoneNumber: string;
    roleId: string;
    locations?: LocationAccessDto[];
}
export declare class CreateUserResponseDto {
    user: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        telephoneNumber: string;
        role: {
            name: string;
            privileges: string[];
        };
        createdAt: Date;
    };
    message: string;
}
