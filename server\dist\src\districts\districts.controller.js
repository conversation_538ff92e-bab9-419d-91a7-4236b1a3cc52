"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistrictsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const districts_service_1 = require("./districts.service");
const auth_guard_1 = require("../common/guards/auth.guard");
const privilege_guard_1 = require("../common/guards/privilege.guard");
const mobile_guard_1 = require("../common/guards/mobile.guard");
const privileges_decorator_1 = require("../common/decorators/privileges.decorator");
const client_1 = require("@prisma/client");
const create_district_dto_1 = require("./dto/create-district.dto");
const update_district_dto_1 = require("./dto/update-district.dto");
const district_response_dto_1 = require("./dto/district-response.dto");
let DistrictsController = class DistrictsController {
    districtsService;
    constructor(districtsService) {
        this.districtsService = districtsService;
    }
    async create(createDistrictDto) {
        return this.districtsService.create(createDistrictDto);
    }
    async findAll(page, limit, search, provinceId) {
        return this.districtsService.findAll(page, limit, search, provinceId);
    }
    async findOne(id) {
        return this.districtsService.findOne(id);
    }
    async update(id, updateDistrictDto) {
        return this.districtsService.update(id, updateDistrictDto);
    }
    async remove(id) {
        return this.districtsService.remove(id);
    }
};
exports.DistrictsController = DistrictsController;
__decorate([
    (0, common_1.Post)(),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new district' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'District created successfully',
        type: create_district_dto_1.CreateDistrictResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Province not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'District already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_district_dto_1.CreateDistrictDto]),
    __metadata("design:returntype", Promise)
], DistrictsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Get all districts with pagination and filtering' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search by district name or code',
        example: 'Nyarugenge',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'provinceId',
        required: false,
        type: Number,
        description: 'Filter by province ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Districts retrieved successfully',
        type: district_response_dto_1.DistrictsListResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Query)('page', new common_1.ParseIntPipe({ optional: true }))),
    __param(1, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('provinceId', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, Number]),
    __metadata("design:returntype", Promise)
], DistrictsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Get a district by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'District ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'District retrieved successfully',
        type: district_response_dto_1.DistrictResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'District not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DistrictsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Update a district' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'District ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'District updated successfully',
        type: update_district_dto_1.UpdateDistrictResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'District or Province not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'District already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_district_dto_1.UpdateDistrictDto]),
    __metadata("design:returntype", Promise)
], DistrictsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a district' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'District ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'District deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'District deleted successfully',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - District has sectors' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'District not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DistrictsController.prototype, "remove", null);
exports.DistrictsController = DistrictsController = __decorate([
    (0, swagger_1.ApiTags)('Districts'),
    (0, common_1.Controller)('districts'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, mobile_guard_1.MobileGuard, privilege_guard_1.PrivilegeGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [districts_service_1.DistrictsService])
], DistrictsController);
//# sourceMappingURL=districts.controller.js.map