export declare class LoginDto {
    email: string;
    password: string;
}
export declare class LoginWith2FADto extends LoginDto {
    totpCode: string;
}
export declare class LoginResponseDto {
    accessToken: string;
    refreshToken: string;
    user: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        role: {
            name: string;
            privileges: string[];
        };
    };
}
export declare class TwoFactorRequiredDto {
    requires2FA: boolean;
    tempToken: string;
    message: string;
}
