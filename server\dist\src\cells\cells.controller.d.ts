import { CellsService } from './cells.service';
import { Create<PERSON>ellDto, CreateCellResponseDto } from './dto/create-cell.dto';
import { UpdateCellDto, UpdateCellResponseDto } from './dto/update-cell.dto';
import { CellResponseDto, CellsListResponseDto } from './dto/cell-response.dto';
export declare class CellsController {
    private readonly cellsService;
    constructor(cellsService: CellsService);
    create(createCellDto: CreateCellDto): Promise<CreateCellResponseDto>;
    findAll(page?: number, limit?: number, search?: string, sectorId?: number): Promise<CellsListResponseDto>;
    findOne(id: number): Promise<CellResponseDto>;
    update(id: number, updateCellDto: UpdateCellDto): Promise<UpdateCellResponseDto>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
