"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheOperations = exports.eventLoopLag = exports.memoryUsage = exports.applicationErrors = exports.emailsSent = exports.locationCoverage = exports.facilityCount = exports.userRegistrations = exports.dataSubmissions = exports.databaseConnections = exports.databaseQueryDuration = exports.activeUserSessions = exports.authenticationAttempts = exports.httpActiveConnections = exports.httpRequestTotal = exports.httpRequestDuration = void 0;
const client = __importStar(require("prom-client"));
client.collectDefaultMetrics({
    prefix: 'wash_mis_',
    labels: { service: 'wash-mis-api' }
});
exports.httpRequestDuration = new client.Histogram({
    name: 'wash_mis_http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code', 'endpoint'],
    buckets: [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
});
exports.httpRequestTotal = new client.Counter({
    name: 'wash_mis_http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'status_code', 'endpoint'],
});
exports.httpActiveConnections = new client.Gauge({
    name: 'wash_mis_http_active_connections',
    help: 'Number of active HTTP connections',
});
exports.authenticationAttempts = new client.Counter({
    name: 'wash_mis_auth_attempts_total',
    help: 'Total number of authentication attempts',
    labelNames: ['type', 'status', 'method'],
});
exports.activeUserSessions = new client.Gauge({
    name: 'wash_mis_active_user_sessions',
    help: 'Number of active user sessions',
    labelNames: ['role'],
});
exports.databaseQueryDuration = new client.Histogram({
    name: 'wash_mis_database_query_duration_seconds',
    help: 'Duration of database queries in seconds',
    labelNames: ['operation', 'table', 'status'],
    buckets: [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5],
});
exports.databaseConnections = new client.Gauge({
    name: 'wash_mis_database_connections',
    help: 'Number of database connections',
    labelNames: ['state'],
});
exports.dataSubmissions = new client.Counter({
    name: 'wash_mis_data_submissions_total',
    help: 'Total number of data submissions',
    labelNames: ['facility_type', 'location_type', 'status'],
});
exports.userRegistrations = new client.Counter({
    name: 'wash_mis_user_registrations_total',
    help: 'Total number of user registrations',
    labelNames: ['role', 'status'],
});
exports.facilityCount = new client.Gauge({
    name: 'wash_mis_facilities_total',
    help: 'Total number of facilities by type',
    labelNames: ['type', 'province', 'district'],
});
exports.locationCoverage = new client.Gauge({
    name: 'wash_mis_location_coverage',
    help: 'Coverage metrics by location',
    labelNames: ['province', 'district', 'sector', 'metric_type'],
});
exports.emailsSent = new client.Counter({
    name: 'wash_mis_emails_sent_total',
    help: 'Total number of emails sent',
    labelNames: ['type', 'status'],
});
exports.applicationErrors = new client.Counter({
    name: 'wash_mis_errors_total',
    help: 'Total number of application errors',
    labelNames: ['type', 'severity', 'module'],
});
exports.memoryUsage = new client.Gauge({
    name: 'wash_mis_memory_usage_bytes',
    help: 'Memory usage in bytes',
    labelNames: ['type'],
});
exports.eventLoopLag = new client.Histogram({
    name: 'wash_mis_event_loop_lag_seconds',
    help: 'Event loop lag in seconds',
    buckets: [0.001, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1],
});
exports.cacheOperations = new client.Counter({
    name: 'wash_mis_cache_operations_total',
    help: 'Total number of cache operations',
    labelNames: ['operation', 'status'],
});
exports.default = client;
//# sourceMappingURL=metrics.js.map