import { PrismaService } from '../prisma/prisma.service';
import { CreateProvinceDto, CreateProvinceResponseDto } from './dto/create-province.dto';
import { UpdateProvinceDto, UpdateProvinceResponseDto } from './dto/update-province.dto';
import { ProvinceResponseDto, ProvincesListResponseDto } from './dto/province-response.dto';
export declare class ProvincesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createProvinceDto: CreateProvinceDto): Promise<CreateProvinceResponseDto>;
    findAll(page?: number, limit?: number, search?: string): Promise<ProvincesListResponseDto>;
    findOne(id: number): Promise<ProvinceResponseDto>;
    update(id: number, updateProvinceDto: UpdateProvinceDto): Promise<UpdateProvinceResponseDto>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
