"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sdk_node_1 = require("@opentelemetry/sdk-node");
const exporter_trace_otlp_http_1 = require("@opentelemetry/exporter-trace-otlp-http");
const instrumentation_express_1 = require("@opentelemetry/instrumentation-express");
const instrumentation_http_1 = require("@opentelemetry/instrumentation-http");
const traceExporter = new exporter_trace_otlp_http_1.OTLPTraceExporter({
    url: process.env.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT || 'http://localhost:4318/v1/traces',
    headers: {},
});
const sdk = new sdk_node_1.NodeSDK({
    serviceName: 'wash-mis-api',
    traceExporter,
    instrumentations: [
        new instrumentation_http_1.HttpInstrumentation({
            requestHook: (span, request) => {
                try {
                    const req = request;
                    if (req.headers) {
                        span.setAttributes({
                            'http.request.header.user-agent': req.headers['user-agent'] || '',
                            'http.request.header.x-forwarded-for': req.headers['x-forwarded-for'] || '',
                        });
                    }
                }
                catch (error) {
                }
            },
            responseHook: (span, response) => {
                try {
                    const res = response;
                    if (res.headers) {
                        span.setAttributes({
                            'http.response.header.content-type': res.headers['content-type'] || '',
                        });
                    }
                }
                catch (error) {
                }
            },
        }),
        new instrumentation_express_1.ExpressInstrumentation({
            requestHook: (span, info) => {
                try {
                    span.setAttributes({
                        'express.route': info.route || '',
                        'express.method': info.request?.method || '',
                    });
                }
                catch (error) {
                }
            },
        }),
    ],
});
sdk.start();
console.log('OpenTelemetry tracing initialized successfully');
exports.default = sdk;
//# sourceMappingURL=tracing.js.map