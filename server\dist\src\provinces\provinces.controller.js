"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProvincesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const provinces_service_1 = require("./provinces.service");
const auth_guard_1 = require("../common/guards/auth.guard");
const privilege_guard_1 = require("../common/guards/privilege.guard");
const mobile_guard_1 = require("../common/guards/mobile.guard");
const privileges_decorator_1 = require("../common/decorators/privileges.decorator");
const client_1 = require("@prisma/client");
const create_province_dto_1 = require("./dto/create-province.dto");
const update_province_dto_1 = require("./dto/update-province.dto");
const province_response_dto_1 = require("./dto/province-response.dto");
let ProvincesController = class ProvincesController {
    provincesService;
    constructor(provincesService) {
        this.provincesService = provincesService;
    }
    async create(createProvinceDto) {
        return this.provincesService.create(createProvinceDto);
    }
    async findAll(page, limit, search) {
        return this.provincesService.findAll(page, limit, search);
    }
    async findOne(id) {
        return this.provincesService.findOne(id);
    }
    async update(id, updateProvinceDto) {
        return this.provincesService.update(id, updateProvinceDto);
    }
    async remove(id) {
        return this.provincesService.remove(id);
    }
};
exports.ProvincesController = ProvincesController;
__decorate([
    (0, common_1.Post)(),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new province' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Province created successfully',
        type: create_province_dto_1.CreateProvinceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Province already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_province_dto_1.CreateProvinceDto]),
    __metadata("design:returntype", Promise)
], ProvincesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Get all provinces with pagination and filtering' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search by province name or code',
        example: 'Kigali',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Provinces retrieved successfully',
        type: province_response_dto_1.ProvincesListResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Query)('page', new common_1.ParseIntPipe({ optional: true }))),
    __param(1, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __param(2, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], ProvincesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Get a province by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Province ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Province retrieved successfully',
        type: province_response_dto_1.ProvinceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Province not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProvincesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Update a province' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Province ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Province updated successfully',
        type: update_province_dto_1.UpdateProvinceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Province not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Province already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_province_dto_1.UpdateProvinceDto]),
    __metadata("design:returntype", Promise)
], ProvincesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a province' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Province ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Province deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Province deleted successfully',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Province has districts' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Province not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProvincesController.prototype, "remove", null);
exports.ProvincesController = ProvincesController = __decorate([
    (0, swagger_1.ApiTags)('Provinces'),
    (0, common_1.Controller)('provinces'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, mobile_guard_1.MobileGuard, privilege_guard_1.PrivilegeGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [provinces_service_1.ProvincesService])
], ProvincesController);
//# sourceMappingURL=provinces.controller.js.map