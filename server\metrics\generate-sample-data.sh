#!/bin/bash

# WASH MIS Sample Data Generator for Testing Monitoring

set -e

echo "🧪 Generating sample data for WASH MIS monitoring..."

# Configuration
API_BASE_URL="http://localhost:8080/api/v1"
REQUESTS_PER_ENDPOINT=10
DELAY_BETWEEN_REQUESTS=0.5

# Check if the API is running
if ! curl -s "$API_BASE_URL" > /dev/null 2>&1; then
    echo "❌ WASH MIS API is not running at $API_BASE_URL"
    echo "   Please start the application first: pnpm run dev"
    exit 1
fi

echo "✅ WASH MIS API is running"

# Function to make HTTP requests
make_requests() {
    local endpoint=$1
    local method=${2:-GET}
    local description=$3
    
    echo "📡 Testing $description ($method $endpoint)..."
    
    for i in $(seq 1 $REQUESTS_PER_ENDPOINT); do
        case $method in
            "GET")
                curl -s -w "Status: %{http_code}, Time: %{time_total}s\n" \
                     -o /dev/null "$API_BASE_URL$endpoint" || true
                ;;
            "POST")
                curl -s -w "Status: %{http_code}, Time: %{time_total}s\n" \
                     -o /dev/null -X POST \
                     -H "Content-Type: application/json" \
                     -d '{}' "$API_BASE_URL$endpoint" || true
                ;;
        esac
        sleep $DELAY_BETWEEN_REQUESTS
    done
}

# Generate traffic for different endpoints
echo "🚀 Starting traffic generation..."

# Health check endpoint
make_requests "/" "GET" "Health Check"

# Metrics endpoint
make_requests "/metrics" "GET" "Metrics Endpoint"

# Authentication endpoints (these will likely fail, but generate metrics)
make_requests "/auth/login" "POST" "Login Attempts"
make_requests "/auth/register" "POST" "Registration Attempts"

# User endpoints (these will likely fail without auth, but generate metrics)
make_requests "/users" "GET" "User List"
make_requests "/users/profile" "GET" "User Profile"

# API documentation
make_requests "/api-docs" "GET" "API Documentation"

# Generate some 404 errors
make_requests "/nonexistent" "GET" "404 Errors"
make_requests "/invalid/endpoint" "GET" "More 404 Errors"

# Generate some slow requests (if available)
make_requests "/slow-endpoint" "GET" "Slow Requests" || true

echo ""
echo "✅ Sample data generation completed!"
echo ""
echo "📊 Check the following in Grafana:"
echo "   1. HTTP Request Rate dashboard"
echo "   2. Response time metrics"
echo "   3. Error rate metrics"
echo "   4. Authentication attempt metrics"
echo ""
echo "🔍 You should now see:"
echo "   - HTTP requests in the metrics"
echo "   - Response time data"
echo "   - Error rate statistics"
echo "   - Log entries in Loki"
echo "   - Trace data in Tempo"
echo ""
echo "💡 To generate continuous traffic, run this script in a loop:"
echo "   while true; do ./generate-sample-data.sh; sleep 30; done"
