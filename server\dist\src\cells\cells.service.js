"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CellsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let CellsService = class CellsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createCellDto) {
        const { code, name, sectorId } = createCellDto;
        const sector = await this.prisma.sector.findUnique({
            where: { id: sectorId },
        });
        if (!sector) {
            throw new common_1.NotFoundException('Sector not found');
        }
        const existingCell = await this.prisma.cell.findFirst({
            where: {
                OR: [
                    { code },
                    { name },
                ],
            },
        });
        if (existingCell) {
            if (existingCell.code === code) {
                throw new common_1.ConflictException('Cell with this code already exists');
            }
            if (existingCell.name === name) {
                throw new common_1.ConflictException('Cell with this name already exists');
            }
        }
        const cell = await this.prisma.cell.create({
            data: {
                code,
                name,
                sectorId,
            },
        });
        return {
            cell: {
                id: cell.id,
                code: cell.code,
                name: cell.name,
                sectorId: cell.sectorId,
                createdAt: cell.createdAt,
            },
            message: 'Cell created successfully',
        };
    }
    async findAll(page = 1, limit = 10, search, sectorId) {
        const skip = (page - 1) * limit;
        const where = {
            AND: [
                search
                    ? {
                        OR: [
                            { name: { contains: search, mode: 'insensitive' } },
                            { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                        ].filter(Boolean),
                    }
                    : {},
                sectorId ? { sectorId } : {},
            ],
        };
        const [cells, total] = await Promise.all([
            this.prisma.cell.findMany({
                where,
                skip,
                take: limit,
                include: {
                    sector: {
                        include: {
                            district: {
                                include: {
                                    province: true,
                                },
                            },
                        },
                    },
                    _count: {
                        select: { villages: true },
                    },
                },
                orderBy: {
                    code: 'asc',
                },
            }),
            this.prisma.cell.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            cells: cells.map((cell) => ({
                id: cell.id,
                code: cell.code,
                name: cell.name,
                sector: {
                    id: cell.sector.id,
                    name: cell.sector.name,
                    code: cell.sector.code,
                    district: {
                        id: cell.sector.district.id,
                        name: cell.sector.district.name,
                        code: cell.sector.district.code,
                        province: {
                            id: cell.sector.district.province.id,
                            name: cell.sector.district.province.name,
                            code: cell.sector.district.province.code,
                        },
                    },
                },
                villageCount: cell._count.villages,
                createdAt: cell.createdAt,
                updatedAt: cell.updatedAt,
            })),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const cell = await this.prisma.cell.findUnique({
            where: { id },
            include: {
                sector: {
                    include: {
                        district: {
                            include: {
                                province: true,
                            },
                        },
                    },
                },
                _count: {
                    select: { villages: true },
                },
            },
        });
        if (!cell) {
            throw new common_1.NotFoundException('Cell not found');
        }
        return {
            id: cell.id,
            code: cell.code,
            name: cell.name,
            sector: {
                id: cell.sector.id,
                name: cell.sector.name,
                code: cell.sector.code,
                district: {
                    id: cell.sector.district.id,
                    name: cell.sector.district.name,
                    code: cell.sector.district.code,
                    province: {
                        id: cell.sector.district.province.id,
                        name: cell.sector.district.province.name,
                        code: cell.sector.district.province.code,
                    },
                },
            },
            villageCount: cell._count.villages,
            createdAt: cell.createdAt,
            updatedAt: cell.updatedAt,
        };
    }
    async update(id, updateCellDto) {
        const { code, name, sectorId } = updateCellDto;
        const existingCell = await this.prisma.cell.findUnique({
            where: { id },
        });
        if (!existingCell) {
            throw new common_1.NotFoundException('Cell not found');
        }
        if (sectorId) {
            const sector = await this.prisma.sector.findUnique({
                where: { id: sectorId },
            });
            if (!sector) {
                throw new common_1.NotFoundException('Sector not found');
            }
        }
        if (code || name) {
            const conflictCell = await this.prisma.cell.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        {
                            OR: [
                                code ? { code } : {},
                                name ? { name } : {},
                            ].filter(Boolean),
                        },
                    ],
                },
            });
            if (conflictCell) {
                if (conflictCell.code === code) {
                    throw new common_1.ConflictException('Cell with this code already exists');
                }
                if (conflictCell.name === name) {
                    throw new common_1.ConflictException('Cell with this name already exists');
                }
            }
        }
        const updatedCell = await this.prisma.cell.update({
            where: { id },
            data: {
                ...(code && { code }),
                ...(name && { name }),
                ...(sectorId && { sectorId }),
            },
        });
        return {
            cell: {
                id: updatedCell.id,
                code: updatedCell.code,
                name: updatedCell.name,
                sectorId: updatedCell.sectorId,
                updatedAt: updatedCell.updatedAt,
            },
            message: 'Cell updated successfully',
        };
    }
    async remove(id) {
        const existingCell = await this.prisma.cell.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { villages: true },
                },
            },
        });
        if (!existingCell) {
            throw new common_1.NotFoundException('Cell not found');
        }
        if (existingCell._count.villages > 0) {
            throw new common_1.BadRequestException(`Cannot delete cell. ${existingCell._count.villages} village(s) belong to this cell`);
        }
        await this.prisma.cell.delete({
            where: { id },
        });
        return { message: 'Cell deleted successfully' };
    }
};
exports.CellsService = CellsService;
exports.CellsService = CellsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CellsService);
//# sourceMappingURL=cells.service.js.map