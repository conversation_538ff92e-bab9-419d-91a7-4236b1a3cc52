"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesListResponseDto = exports.RoleResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
class RoleResponseDto {
    id;
    name;
    code;
    privileges;
    userCount;
    createdAt;
    updatedAt;
}
exports.RoleResponseDto = RoleResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role ID',
        example: 'role_123',
    }),
    __metadata("design:type", String)
], RoleResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role name',
        example: 'Data Collector',
    }),
    __metadata("design:type", String)
], RoleResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role code',
        example: 'DATA_COLLECTOR',
    }),
    __metadata("design:type", String)
], RoleResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of privileges assigned to this role',
        example: [client_1.Privilege.DATA_COLLECTION],
        enum: client_1.Privilege,
        isArray: true,
    }),
    __metadata("design:type", Array)
], RoleResponseDto.prototype, "privileges", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of users assigned to this role',
        example: 5,
    }),
    __metadata("design:type", Number)
], RoleResponseDto.prototype, "userCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], RoleResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], RoleResponseDto.prototype, "updatedAt", void 0);
class RolesListResponseDto {
    roles;
    total;
    page;
    limit;
    totalPages;
}
exports.RolesListResponseDto = RolesListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of roles',
        type: [RoleResponseDto],
    }),
    __metadata("design:type", Array)
], RolesListResponseDto.prototype, "roles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of roles',
        example: 25,
    }),
    __metadata("design:type", Number)
], RolesListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], RolesListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], RolesListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 3,
    }),
    __metadata("design:type", Number)
], RolesListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=role-response.dto.js.map