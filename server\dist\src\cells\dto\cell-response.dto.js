"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CellsListResponseDto = exports.CellResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CellResponseDto {
    id;
    code;
    name;
    sector;
    villageCount;
    createdAt;
    updatedAt;
}
exports.CellResponseDto = CellResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell ID',
        example: 1,
    }),
    __metadata("design:type", Number)
], CellResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell code',
        example: 1010101,
    }),
    __metadata("design:type", Number)
], CellResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell name',
        example: 'Gisozi',
    }),
    __metadata("design:type", String)
], CellResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector information',
    }),
    __metadata("design:type", Object)
], CellResponseDto.prototype, "sector", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of villages in this cell',
        example: 8,
    }),
    __metadata("design:type", Number)
], CellResponseDto.prototype, "villageCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], CellResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], CellResponseDto.prototype, "updatedAt", void 0);
class CellsListResponseDto {
    cells;
    total;
    page;
    limit;
    totalPages;
}
exports.CellsListResponseDto = CellsListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of cells',
        type: [CellResponseDto],
    }),
    __metadata("design:type", Array)
], CellsListResponseDto.prototype, "cells", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of cells',
        example: 2148,
    }),
    __metadata("design:type", Number)
], CellsListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], CellsListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], CellsListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 215,
    }),
    __metadata("design:type", Number)
], CellsListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=cell-response.dto.js.map