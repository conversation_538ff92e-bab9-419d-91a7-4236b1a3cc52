"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistrictsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let DistrictsService = class DistrictsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createDistrictDto) {
        const { code, name, provinceId } = createDistrictDto;
        const province = await this.prisma.province.findUnique({
            where: { id: provinceId },
        });
        if (!province) {
            throw new common_1.NotFoundException('Province not found');
        }
        const existingDistrict = await this.prisma.district.findFirst({
            where: {
                OR: [
                    { code },
                    { name },
                ],
            },
        });
        if (existingDistrict) {
            if (existingDistrict.code === code) {
                throw new common_1.ConflictException('District with this code already exists');
            }
            if (existingDistrict.name === name) {
                throw new common_1.ConflictException('District with this name already exists');
            }
        }
        const district = await this.prisma.district.create({
            data: {
                code,
                name,
                provinceId,
            },
        });
        return {
            district: {
                id: district.id,
                code: district.code,
                name: district.name,
                provinceId: district.provinceId,
                createdAt: district.createdAt,
            },
            message: 'District created successfully',
        };
    }
    async findAll(page = 1, limit = 10, search, provinceId) {
        const skip = (page - 1) * limit;
        const where = {
            AND: [
                search
                    ? {
                        OR: [
                            { name: { contains: search, mode: 'insensitive' } },
                            { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                        ].filter(Boolean),
                    }
                    : {},
                provinceId ? { provinceId } : {},
            ],
        };
        const [districts, total] = await Promise.all([
            this.prisma.district.findMany({
                where,
                skip,
                take: limit,
                include: {
                    province: true,
                    _count: {
                        select: { sectors: true },
                    },
                },
                orderBy: {
                    code: 'asc',
                },
            }),
            this.prisma.district.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            districts: districts.map((district) => ({
                id: district.id,
                code: district.code,
                name: district.name,
                province: {
                    id: district.province.id,
                    name: district.province.name,
                    code: district.province.code,
                },
                sectorCount: district._count.sectors,
                createdAt: district.createdAt,
                updatedAt: district.updatedAt,
            })),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const district = await this.prisma.district.findUnique({
            where: { id },
            include: {
                province: true,
                _count: {
                    select: { sectors: true },
                },
            },
        });
        if (!district) {
            throw new common_1.NotFoundException('District not found');
        }
        return {
            id: district.id,
            code: district.code,
            name: district.name,
            province: {
                id: district.province.id,
                name: district.province.name,
                code: district.province.code,
            },
            sectorCount: district._count.sectors,
            createdAt: district.createdAt,
            updatedAt: district.updatedAt,
        };
    }
    async update(id, updateDistrictDto) {
        const { code, name, provinceId } = updateDistrictDto;
        const existingDistrict = await this.prisma.district.findUnique({
            where: { id },
        });
        if (!existingDistrict) {
            throw new common_1.NotFoundException('District not found');
        }
        if (provinceId) {
            const province = await this.prisma.province.findUnique({
                where: { id: provinceId },
            });
            if (!province) {
                throw new common_1.NotFoundException('Province not found');
            }
        }
        if (code || name) {
            const conflictDistrict = await this.prisma.district.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        {
                            OR: [
                                code ? { code } : {},
                                name ? { name } : {},
                            ].filter(Boolean),
                        },
                    ],
                },
            });
            if (conflictDistrict) {
                if (conflictDistrict.code === code) {
                    throw new common_1.ConflictException('District with this code already exists');
                }
                if (conflictDistrict.name === name) {
                    throw new common_1.ConflictException('District with this name already exists');
                }
            }
        }
        const updatedDistrict = await this.prisma.district.update({
            where: { id },
            data: {
                ...(code && { code }),
                ...(name && { name }),
                ...(provinceId && { provinceId }),
            },
        });
        return {
            district: {
                id: updatedDistrict.id,
                code: updatedDistrict.code,
                name: updatedDistrict.name,
                provinceId: updatedDistrict.provinceId,
                updatedAt: updatedDistrict.updatedAt,
            },
            message: 'District updated successfully',
        };
    }
    async remove(id) {
        const existingDistrict = await this.prisma.district.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { sectors: true },
                },
            },
        });
        if (!existingDistrict) {
            throw new common_1.NotFoundException('District not found');
        }
        if (existingDistrict._count.sectors > 0) {
            throw new common_1.BadRequestException(`Cannot delete district. ${existingDistrict._count.sectors} sector(s) belong to this district`);
        }
        await this.prisma.district.delete({
            where: { id },
        });
        return { message: 'District deleted successfully' };
    }
};
exports.DistrictsService = DistrictsService;
exports.DistrictsService = DistrictsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DistrictsService);
//# sourceMappingURL=districts.service.js.map