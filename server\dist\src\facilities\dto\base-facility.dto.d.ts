export declare class BaseFacilityDto {
    locationId: string;
    number: number;
}
export declare class BaseFacilityWithNameDto extends BaseFacilityDto {
    name: string;
}
export declare class PaginationQueryDto {
    page?: number;
    limit?: number;
    search?: string;
}
export declare class BaseFacilityResponseDto {
    id: string;
    number: number;
    location: {
        id: string;
        villageId: number;
        village: {
            id: number;
            name: string;
            cell: {
                id: number;
                name: string;
                sector: {
                    id: number;
                    name: string;
                    district: {
                        id: number;
                        name: string;
                        province: {
                            id: number;
                            name: string;
                        };
                    };
                };
            };
        };
        latitude?: number;
        longitude?: number;
        settlementType: string;
    };
    createdAt: Date;
    updatedAt: Date;
}
export declare class BaseFacilityWithNameResponseDto extends BaseFacilityResponseDto {
    name: string;
}
export declare class BasePaginatedResponseDto<T> {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    data: T[];
}
export declare class BaseCreateResponseDto<T> {
    message: string;
    facility: T;
}
export declare class BaseUpdateResponseDto<T> {
    message: string;
    facility: T;
}
export declare class BaseDeleteResponseDto {
    message: string;
}
