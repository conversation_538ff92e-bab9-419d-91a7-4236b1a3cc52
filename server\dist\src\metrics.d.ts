import * as client from 'prom-client';
export declare const httpRequestDuration: client.Histogram<"method" | "route" | "status_code" | "endpoint">;
export declare const httpRequestTotal: client.Counter<"method" | "route" | "status_code" | "endpoint">;
export declare const httpActiveConnections: client.Gauge<string>;
export declare const authenticationAttempts: client.Counter<"method" | "type" | "status">;
export declare const activeUserSessions: client.Gauge<"role">;
export declare const databaseQueryDuration: client.Histogram<"status" | "operation" | "table">;
export declare const databaseConnections: client.Gauge<"state">;
export declare const dataSubmissions: client.Counter<"status" | "facility_type" | "location_type">;
export declare const userRegistrations: client.Counter<"role" | "status">;
export declare const facilityCount: client.Gauge<"province" | "district" | "type">;
export declare const locationCoverage: client.Gauge<"province" | "district" | "sector" | "metric_type">;
export declare const emailsSent: client.Counter<"type" | "status">;
export declare const applicationErrors: client.Counter<"type" | "severity" | "module">;
export declare const memoryUsage: client.Gauge<"type">;
export declare const eventLoopLag: client.Histogram<string>;
export declare const cacheOperations: client.Counter<"status" | "operation">;
export default client;
