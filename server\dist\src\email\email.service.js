"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const template_service_1 = require("./template.service");
let EmailService = EmailService_1 = class EmailService {
    httpService;
    configService;
    templateService;
    logger = new common_1.Logger(EmailService_1.name);
    constructor(httpService, configService, templateService) {
        this.httpService = httpService;
        this.configService = configService;
        this.templateService = templateService;
    }
    async sendEmail(to, receiverName, subject, templateName, templateContext) {
        try {
            const body = await this.templateService.renderTemplate(templateName, templateContext);
            const requestData = {
                sender_name: this.configService.get('EMAIL_SENDER_NAME'),
                sender_email: this.configService.get('EMAIL_SENDER'),
                receiver_name: receiverName,
                receiver_email: to,
                subject: subject,
                message: body,
            };
            await (0, rxjs_1.firstValueFrom)(this.httpService.post(this.configService.get('EMAIL_API_URL'), requestData));
            this.logger.log(`Email sent to ${to}`, 'EmailService');
        }
        catch (error) {
            this.logger.error(`Failed to send email to ${to}:`, error);
            throw error;
        }
    }
    async sendUserCreationEmail(email, firstName, verificationToken) {
        const verificationUrl = `${this.configService.get('FRONTEND_URL')}/auth/set-password?token=${verificationToken}`;
        await this.sendEmail(email, firstName, 'Welcome! Set up your account', 'user-creation', {
            firstName,
            verificationUrl,
            verificationToken,
        });
    }
    async sendPasswordResetEmail(email, firstName, resetToken) {
        const resetUrl = `${this.configService.get('FRONTEND_URL')}/auth/reset-password?token=${resetToken}`;
        await this.sendEmail(email, firstName, 'Password Reset Request', 'password-reset', {
            firstName,
            resetUrl,
            resetToken,
        });
    }
    async send2FASetupEmail(email, firstName) {
        const setupUrl = `${this.configService.get('FRONTEND_URL')}/auth/2fa-setup`;
        await this.sendEmail(email, firstName, 'Set up Two-Factor Authentication', '2fa-setup', {
            firstName,
            setupUrl,
        });
    }
    async sendAccountVerificationEmail(email, firstName, verificationToken) {
        const verificationUrl = `${this.configService.get('FRONTEND_URL')}/auth/verify-account?token=${verificationToken}`;
        await this.sendEmail(email, firstName, 'Verify your account', 'account-verification', {
            firstName,
            verificationUrl,
            verificationToken,
        });
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = EmailService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService,
        config_1.ConfigService,
        template_service_1.TemplateService])
], EmailService);
//# sourceMappingURL=email.service.js.map