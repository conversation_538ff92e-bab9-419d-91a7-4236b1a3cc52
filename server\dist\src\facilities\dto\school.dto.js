"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSchoolResponseDto = exports.CreateSchoolResponseDto = exports.SchoolsListResponseDto = exports.SchoolResponseDto = exports.UpdateSchoolDto = exports.CreateSchoolDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const base_facility_dto_1 = require("./base-facility.dto");
class CreateSchoolDto extends base_facility_dto_1.BaseFacilityWithNameDto {
}
exports.CreateSchoolDto = CreateSchoolDto;
class UpdateSchoolDto extends (0, swagger_1.PartialType)(CreateSchoolDto) {
}
exports.UpdateSchoolDto = UpdateSchoolDto;
class SchoolResponseDto extends base_facility_dto_1.BaseFacilityWithNameResponseDto {
}
exports.SchoolResponseDto = SchoolResponseDto;
class SchoolsListResponseDto extends base_facility_dto_1.BasePaginatedResponseDto {
}
exports.SchoolsListResponseDto = SchoolsListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of schools',
        type: [SchoolResponseDto],
    }),
    __metadata("design:type", Array)
], SchoolsListResponseDto.prototype, "data", void 0);
class CreateSchoolResponseDto extends base_facility_dto_1.BaseCreateResponseDto {
}
exports.CreateSchoolResponseDto = CreateSchoolResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created school information',
    }),
    __metadata("design:type", SchoolResponseDto)
], CreateSchoolResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'School created successfully',
    }),
    __metadata("design:type", String)
], CreateSchoolResponseDto.prototype, "message", void 0);
class UpdateSchoolResponseDto extends base_facility_dto_1.BaseUpdateResponseDto {
}
exports.UpdateSchoolResponseDto = UpdateSchoolResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated school information',
    }),
    __metadata("design:type", SchoolResponseDto)
], UpdateSchoolResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'School updated successfully',
    }),
    __metadata("design:type", String)
], UpdateSchoolResponseDto.prototype, "message", void 0);
//# sourceMappingURL=school.dto.js.map