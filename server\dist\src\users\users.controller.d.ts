import { UsersService } from './users.service';
import { CreateUserDto, CreateUserResponseDto } from './dto/create-user.dto';
import { UpdateUserDto, UpdateUserResponseDto } from './dto/update-user.dto';
import { UserResponseDto, UsersListResponseDto } from './dto/user-response.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto): Promise<CreateUserResponseDto>;
    findAll(page?: string, limit?: string, search?: string, roleId?: string): Promise<UsersListResponseDto>;
    getProfile(user: any): Promise<UserResponseDto>;
    findOne(id: string): Promise<UserResponseDto>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<UpdateUserResponseDto>;
    remove(id: string): Promise<{
        message: string;
    }>;
    resendVerificationEmail(id: string): Promise<{
        message: string;
    }>;
}
