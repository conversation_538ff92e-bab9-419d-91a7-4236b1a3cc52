# Mimir configuration for WASH MIS monitoring
target: all

server:
  http_listen_port: 9009
  grpc_listen_port: 9095

common:
  storage:
    backend: filesystem
    filesystem:
      dir: /data

blocks_storage:
  backend: filesystem
  filesystem:
    dir: /data/blocks

ruler_storage:
  backend: filesystem
  filesystem:
    dir: /data/ruler

alertmanager_storage:
  backend: filesystem
  filesystem:
    dir: /data/alertmanager

ingester:
  ring:
    replication_factor: 1
    kvstore:
      store: inmemory

distributor:
  ring:
    kvstore:
      store: inmemory

ruler:
  enable_api: true
  ring:
    kvstore:
      store: inmemory

compactor:
  data_dir: /data/compactor
  sharding_ring:
    kvstore:
      store: inmemory

store_gateway:
  sharding_ring:
    replication_factor: 1
    kvstore:
      store: inmemory

limits:
  ingestion_rate: 100000
  ingestion_burst_size: 200000
  max_global_series_per_user: 1000000
  max_global_series_per_metric: 100000
  compactor_blocks_retention_period: 720h
  max_query_parallelism: 32

runtime_config:
  file: ""

memberlist:
  abort_if_cluster_join_fails: false
  bind_port: 7946
  join_members: []

activity_tracker:
  filepath: /data/activity.log
