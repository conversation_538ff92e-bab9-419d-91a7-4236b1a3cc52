import { BaseFacilityWithNameDto, BaseFacilityWithNameResponseDto, BasePaginatedResponseDto, BaseCreateResponseDto, BaseUpdateResponseDto } from './base-facility.dto';
export declare class CreateMarketDto extends BaseFacilityWithNameDto {
}
declare const UpdateMarketDto_base: import("@nestjs/common").Type<Partial<CreateMarketDto>>;
export declare class UpdateMarketDto extends UpdateMarketDto_base {
}
export declare class MarketResponseDto extends BaseFacilityWithNameResponseDto {
}
export declare class MarketsListResponseDto extends BasePaginatedResponseDto<MarketResponseDto> {
    data: MarketResponseDto[];
}
export declare class CreateMarketResponseDto extends BaseCreateResponseDto<MarketResponseDto> {
    facility: MarketResponseDto;
    message: string;
}
export declare class UpdateMarketResponseDto extends BaseUpdateResponseDto<MarketResponseDto> {
    facility: MarketResponseDto;
    message: string;
}
export {};
