"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VillagesListResponseDto = exports.VillageResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class VillageResponseDto {
    id;
    code;
    name;
    cell;
    createdAt;
    updatedAt;
}
exports.VillageResponseDto = VillageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Village ID',
        example: 1,
    }),
    __metadata("design:type", Number)
], VillageResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Village code',
        example: 101010101,
    }),
    __metadata("design:type", Number)
], VillageResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Village name',
        example: 'Ubumwe',
    }),
    __metadata("design:type", String)
], VillageResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell information',
    }),
    __metadata("design:type", Object)
], VillageResponseDto.prototype, "cell", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Village creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], VillageResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Village last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], VillageResponseDto.prototype, "updatedAt", void 0);
class VillagesListResponseDto {
    villages;
    total;
    page;
    limit;
    totalPages;
}
exports.VillagesListResponseDto = VillagesListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of villages',
        type: [VillageResponseDto],
    }),
    __metadata("design:type", Array)
], VillagesListResponseDto.prototype, "villages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of villages',
        example: 14837,
    }),
    __metadata("design:type", Number)
], VillagesListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], VillagesListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], VillagesListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 1484,
    }),
    __metadata("design:type", Number)
], VillagesListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=village-response.dto.js.map