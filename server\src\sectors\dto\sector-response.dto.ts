import { ApiProperty } from '@nestjs/swagger';

export class SectorResponseDto {
  @ApiProperty({
    description: 'Sector ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Sector code',
    example: 10101,
  })
  code: number;

  @ApiProperty({
    description: 'Sector name',
    example: 'Gitega',
  })
  name: string;

  @ApiProperty({
    description: 'District information',
  })
  district: {
    id: number;
    name: string;
    code: number;
    province: {
      id: number;
      name: string;
      code: number;
    };
  };

  @ApiProperty({
    description: 'Number of cells in this sector',
    example: 5,
  })
  cellCount: number;

  @ApiProperty({
    description: 'Sector creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Sector last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class SectorsListResponseDto {
  @ApiProperty({
    description: 'List of sectors',
    type: [SectorResponseDto],
  })
  sectors: SectorResponseDto[];

  @ApiProperty({
    description: 'Total number of sectors',
    example: 416,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 42,
  })
  totalPages: number;
}
