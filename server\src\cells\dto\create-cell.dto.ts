import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsInt,
  MinLength,
  Min,
} from 'class-validator';

export class CreateCellDto {
  @ApiProperty({
    description: 'Cell code (unique identifier)',
    example: 1010101,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({
    description: 'Cell name',
    example: '<PERSON>isoz<PERSON>',
    minLength: 2,
  })
  @IsString()
  @MinLength(2)
  name: string;

  @ApiProperty({
    description: 'Sector ID this cell belongs to',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  sectorId: number;
}

export class CreateCellResponseDto {
  @ApiProperty({
    description: 'Created cell information',
  })
  cell: {
    id: number;
    code: number;
    name: string;
    sectorId: number;
    createdAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Cell created successfully',
  })
  message: string;
}
