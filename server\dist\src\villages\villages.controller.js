"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VillagesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const villages_service_1 = require("./villages.service");
const auth_guard_1 = require("../common/guards/auth.guard");
const privilege_guard_1 = require("../common/guards/privilege.guard");
const mobile_guard_1 = require("../common/guards/mobile.guard");
const privileges_decorator_1 = require("../common/decorators/privileges.decorator");
const client_1 = require("@prisma/client");
const create_village_dto_1 = require("./dto/create-village.dto");
const update_village_dto_1 = require("./dto/update-village.dto");
const village_response_dto_1 = require("./dto/village-response.dto");
let VillagesController = class VillagesController {
    villagesService;
    constructor(villagesService) {
        this.villagesService = villagesService;
    }
    async create(createVillageDto) {
        return this.villagesService.create(createVillageDto);
    }
    async findAll(page, limit, search, cellId) {
        return this.villagesService.findAll(page, limit, search, cellId);
    }
    async findOne(id) {
        return this.villagesService.findOne(id);
    }
    async update(id, updateVillageDto) {
        return this.villagesService.update(id, updateVillageDto);
    }
    async remove(id) {
        return this.villagesService.remove(id);
    }
};
exports.VillagesController = VillagesController;
__decorate([
    (0, common_1.Post)(),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new village' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Village created successfully',
        type: create_village_dto_1.CreateVillageResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cell not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Village already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_village_dto_1.CreateVillageDto]),
    __metadata("design:returntype", Promise)
], VillagesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Get all villages with pagination and filtering' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search by village name or code',
        example: 'Ubumwe',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'cellId',
        required: false,
        type: Number,
        description: 'Filter by cell ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Villages retrieved successfully',
        type: village_response_dto_1.VillagesListResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Query)('page', new common_1.ParseIntPipe({ optional: true }))),
    __param(1, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('cellId', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, Number]),
    __metadata("design:returntype", Promise)
], VillagesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Get a village by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Village ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Village retrieved successfully',
        type: village_response_dto_1.VillageResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Village not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VillagesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Update a village' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Village ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Village updated successfully',
        type: update_village_dto_1.UpdateVillageResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Village or Cell not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Village already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_village_dto_1.UpdateVillageDto]),
    __metadata("design:returntype", Promise)
], VillagesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.USER_MANAGEMENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a village' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Village ID',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Village deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Village deleted successfully',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Village not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VillagesController.prototype, "remove", null);
exports.VillagesController = VillagesController = __decorate([
    (0, swagger_1.ApiTags)('Villages'),
    (0, common_1.Controller)('villages'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, mobile_guard_1.MobileGuard, privilege_guard_1.PrivilegeGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [villages_service_1.VillagesService])
], VillagesController);
//# sourceMappingURL=villages.controller.js.map