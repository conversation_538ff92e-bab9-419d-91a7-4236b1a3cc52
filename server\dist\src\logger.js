"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createModuleLogger = exports.logger = void 0;
const winston = __importStar(require("winston"));
const winston_loki_1 = __importDefault(require("winston-loki"));
const api_1 = require("@opentelemetry/api");
const traceFormat = winston.format((info) => {
    const span = api_1.trace.getActiveSpan();
    if (span) {
        const spanContext = span.spanContext();
        info.trace_id = spanContext.traceId;
        info.span_id = spanContext.spanId;
    }
    return info;
});
const structuredFormat = winston.format.combine(winston.format.timestamp(), traceFormat(), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf(({ timestamp, level, message, trace_id, span_id, service, module, ...meta }) => {
    const logEntry = {
        timestamp,
        level,
        message,
        service: service || 'wash-mis-api',
        module: module || 'unknown',
        ...(trace_id && { trace_id }),
        ...(span_id && { span_id }),
        ...meta,
    };
    return JSON.stringify(logEntry);
}));
const consoleFormat = winston.format.combine(winston.format.colorize(), winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), traceFormat(), winston.format.printf(({ timestamp, level, message, trace_id, service, module, ...meta }) => {
    const traceInfo = trace_id ? ` [trace:${String(trace_id).substring(0, 8)}]` : '';
    const serviceInfo = service ? ` [${service}]` : '';
    const moduleInfo = module ? ` [${module}]` : '';
    const metaInfo = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    return `${timestamp} ${level}${serviceInfo}${moduleInfo}${traceInfo}: ${message}${metaInfo}`;
}));
const transports = [];
if (process.env.NODE_ENV !== 'production') {
    transports.push(new winston.transports.Console({
        format: consoleFormat,
        level: 'debug',
    }));
}
if (process.env.LOKI_URL || process.env.NODE_ENV === 'production') {
    transports.push(new winston_loki_1.default({
        host: process.env.LOKI_URL || 'http://localhost:3100',
        labels: {
            job: 'wash-mis-api',
            service: 'wash-mis-api',
            environment: process.env.NODE_ENV || 'development',
        },
        format: structuredFormat,
        level: 'info',
        onConnectionError: (err) => {
            console.error('Loki connection error:', err);
        },
    }));
}
transports.push(new winston.transports.File({
    filename: 'logs/wash-mis-error.log',
    level: 'error',
    format: structuredFormat,
    maxsize: 5242880,
    maxFiles: 5,
}), new winston.transports.File({
    filename: 'logs/wash-mis-combined.log',
    format: structuredFormat,
    maxsize: 5242880,
    maxFiles: 5,
}));
exports.logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: structuredFormat,
    defaultMeta: {
        service: 'wash-mis-api',
        environment: process.env.NODE_ENV || 'development',
    },
    transports,
    exceptionHandlers: [
        new winston.transports.File({ filename: 'logs/exceptions.log' })
    ],
    rejectionHandlers: [
        new winston.transports.File({ filename: 'logs/rejections.log' })
    ],
});
const createModuleLogger = (module) => {
    return {
        debug: (message, meta) => exports.logger.debug(message, { module, ...meta }),
        info: (message, meta) => exports.logger.info(message, { module, ...meta }),
        warn: (message, meta) => exports.logger.warn(message, { module, ...meta }),
        error: (message, meta) => exports.logger.error(message, { module, ...meta }),
    };
};
exports.createModuleLogger = createModuleLogger;
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map