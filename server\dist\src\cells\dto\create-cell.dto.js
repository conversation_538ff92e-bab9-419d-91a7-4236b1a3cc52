"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCellResponseDto = exports.CreateCellDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCellDto {
    code;
    name;
    sectorId;
}
exports.CreateCellDto = CreateCellDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell code (unique identifier)',
        example: 1010101,
        minimum: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateCellDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cell name',
        example: 'Gisozi',
        minLength: 2,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    __metadata("design:type", String)
], CreateCellDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sector ID this cell belongs to',
        example: 1,
        minimum: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateCellDto.prototype, "sectorId", void 0);
class CreateCellResponseDto {
    cell;
    message;
}
exports.CreateCellResponseDto = CreateCellResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created cell information',
    }),
    __metadata("design:type", Object)
], CreateCellResponseDto.prototype, "cell", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Cell created successfully',
    }),
    __metadata("design:type", String)
], CreateCellResponseDto.prototype, "message", void 0);
//# sourceMappingURL=create-cell.dto.js.map