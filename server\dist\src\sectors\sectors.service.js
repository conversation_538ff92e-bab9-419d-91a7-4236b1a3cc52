"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SectorsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let SectorsService = class SectorsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createSectorDto) {
        const { code, name, districtId } = createSectorDto;
        const district = await this.prisma.district.findUnique({
            where: { id: districtId },
        });
        if (!district) {
            throw new common_1.NotFoundException('District not found');
        }
        const existingSector = await this.prisma.sector.findFirst({
            where: {
                OR: [
                    { code },
                    { name },
                ],
            },
        });
        if (existingSector) {
            if (existingSector.code === code) {
                throw new common_1.ConflictException('Sector with this code already exists');
            }
            if (existingSector.name === name) {
                throw new common_1.ConflictException('Sector with this name already exists');
            }
        }
        const sector = await this.prisma.sector.create({
            data: {
                code,
                name,
                districtId,
            },
        });
        return {
            sector: {
                id: sector.id,
                code: sector.code,
                name: sector.name,
                districtId: sector.districtId,
                createdAt: sector.createdAt,
            },
            message: 'Sector created successfully',
        };
    }
    async findAll(page = 1, limit = 10, search, districtId) {
        const skip = (page - 1) * limit;
        const where = {
            AND: [
                search
                    ? {
                        OR: [
                            { name: { contains: search, mode: 'insensitive' } },
                            { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                        ].filter(Boolean),
                    }
                    : {},
                districtId ? { districtId } : {},
            ],
        };
        const [sectors, total] = await Promise.all([
            this.prisma.sector.findMany({
                where,
                skip,
                take: limit,
                include: {
                    district: {
                        include: {
                            province: true,
                        },
                    },
                    _count: {
                        select: { cells: true },
                    },
                },
                orderBy: {
                    code: 'asc',
                },
            }),
            this.prisma.sector.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            sectors: sectors.map((sector) => ({
                id: sector.id,
                code: sector.code,
                name: sector.name,
                district: {
                    id: sector.district.id,
                    name: sector.district.name,
                    code: sector.district.code,
                    province: {
                        id: sector.district.province.id,
                        name: sector.district.province.name,
                        code: sector.district.province.code,
                    },
                },
                cellCount: sector._count.cells,
                createdAt: sector.createdAt,
                updatedAt: sector.updatedAt,
            })),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const sector = await this.prisma.sector.findUnique({
            where: { id },
            include: {
                district: {
                    include: {
                        province: true,
                    },
                },
                _count: {
                    select: { cells: true },
                },
            },
        });
        if (!sector) {
            throw new common_1.NotFoundException('Sector not found');
        }
        return {
            id: sector.id,
            code: sector.code,
            name: sector.name,
            district: {
                id: sector.district.id,
                name: sector.district.name,
                code: sector.district.code,
                province: {
                    id: sector.district.province.id,
                    name: sector.district.province.name,
                    code: sector.district.province.code,
                },
            },
            cellCount: sector._count.cells,
            createdAt: sector.createdAt,
            updatedAt: sector.updatedAt,
        };
    }
    async update(id, updateSectorDto) {
        const { code, name, districtId } = updateSectorDto;
        const existingSector = await this.prisma.sector.findUnique({
            where: { id },
        });
        if (!existingSector) {
            throw new common_1.NotFoundException('Sector not found');
        }
        if (districtId) {
            const district = await this.prisma.district.findUnique({
                where: { id: districtId },
            });
            if (!district) {
                throw new common_1.NotFoundException('District not found');
            }
        }
        if (code || name) {
            const conflictSector = await this.prisma.sector.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        {
                            OR: [
                                code ? { code } : {},
                                name ? { name } : {},
                            ].filter(Boolean),
                        },
                    ],
                },
            });
            if (conflictSector) {
                if (conflictSector.code === code) {
                    throw new common_1.ConflictException('Sector with this code already exists');
                }
                if (conflictSector.name === name) {
                    throw new common_1.ConflictException('Sector with this name already exists');
                }
            }
        }
        const updatedSector = await this.prisma.sector.update({
            where: { id },
            data: {
                ...(code && { code }),
                ...(name && { name }),
                ...(districtId && { districtId }),
            },
        });
        return {
            sector: {
                id: updatedSector.id,
                code: updatedSector.code,
                name: updatedSector.name,
                districtId: updatedSector.districtId,
                updatedAt: updatedSector.updatedAt,
            },
            message: 'Sector updated successfully',
        };
    }
    async remove(id) {
        const existingSector = await this.prisma.sector.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { cells: true },
                },
            },
        });
        if (!existingSector) {
            throw new common_1.NotFoundException('Sector not found');
        }
        if (existingSector._count.cells > 0) {
            throw new common_1.BadRequestException(`Cannot delete sector. ${existingSector._count.cells} cell(s) belong to this sector`);
        }
        await this.prisma.sector.delete({
            where: { id },
        });
        return { message: 'Sector deleted successfully' };
    }
};
exports.SectorsService = SectorsService;
exports.SectorsService = SectorsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SectorsService);
//# sourceMappingURL=sectors.service.js.map