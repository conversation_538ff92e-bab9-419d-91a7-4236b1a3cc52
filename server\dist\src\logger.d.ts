import * as winston from 'winston';
export declare const logger: winston.Logger;
export declare const createModuleLogger: (module: string) => {
    debug: (message: string, meta?: any) => winston.Logger;
    info: (message: string, meta?: any) => winston.Logger;
    warn: (message: string, meta?: any) => winston.Logger;
    error: (message: string, meta?: any) => winston.Logger;
};
export default logger;
