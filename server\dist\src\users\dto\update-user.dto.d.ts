import { CreateUserDto, LocationAccessDto } from './create-user.dto';
declare const UpdateUserDto_base: import("@nestjs/common").Type<Partial<CreateUserDto>>;
export declare class UpdateUserDto extends UpdateUserDto_base {
    locations?: LocationAccessDto[];
}
export declare class UpdateUserResponseDto {
    user: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        telephoneNumber: string;
        role: {
            name: string;
            privileges: string[];
        };
        updatedAt: Date;
    };
    message: string;
}
export {};
