import { ApiProperty, PartialType } from '@nestjs/swagger';
import { 
  BaseFacilityWithNameDto, 
  BaseFacilityWithNameResponseDto, 
  BasePaginatedResponseDto,
  BaseCreateResponseDto,
  BaseUpdateResponseDto
} from './base-facility.dto';

export class CreateHealthFacilityDto extends BaseFacilityWithNameDto {}

export class UpdateHealthFacilityDto extends PartialType(CreateHealthFacilityDto) {}

export class HealthFacilityResponseDto extends BaseFacilityWithNameResponseDto {}

export class HealthFacilitiesListResponseDto extends BasePaginatedResponseDto<HealthFacilityResponseDto> {
  @ApiProperty({
    description: 'List of health facilities',
    type: [HealthFacilityResponseDto],
  })
  declare data: HealthFacilityResponseDto[];
}

export class CreateHealthFacilityResponseDto extends BaseCreateResponseDto<HealthFacilityResponseDto> {
  @ApiProperty({
    description: 'Created health facility information',
  })
  declare facility: HealthFacilityResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'Health facility created successfully',
  })
  declare message: string;
}

export class UpdateHealthFacilityResponseDto extends BaseUpdateResponseDto<HealthFacilityResponseDto> {
  @ApiProperty({
    description: 'Updated health facility information',
  })
  declare facility: HealthFacilityResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'Health facility updated successfully',
  })
  declare message: string;
}
