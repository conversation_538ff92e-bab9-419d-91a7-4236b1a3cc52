import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { EmailService } from '../email/email.service';
import { LoginDto, LoginWith2FADto, LoginResponseDto, TwoFactorRequiredDto } from './dto/login.dto';
import { RequestPasswordResetDto, ResetPasswordDto, SetInitialPasswordDto } from './dto/password-reset.dto';
import { Setup2FADto, Verify2FADto, UseRecoveryCodeDto, Setup2FAResponseDto, Enable2FAResponseDto } from './dto/two-factor.dto';
import { RefreshTokenDto, RefreshTokenResponseDto } from './dto/refresh-token.dto';
export declare class AuthService {
    private prisma;
    private jwtService;
    private configService;
    private emailService;
    constructor(prisma: PrismaService, jwtService: JwtService, configService: ConfigService, emailService: EmailService);
    login(loginDto: LoginDto): Promise<LoginResponseDto | TwoFactorRequiredDto>;
    loginWith2FA(loginWith2FADto: LoginWith2FADto): Promise<LoginResponseDto>;
    verify2FA(verify2FADto: Verify2FADto): Promise<LoginResponseDto>;
    useRecoveryCode(useRecoveryCodeDto: UseRecoveryCodeDto): Promise<LoginResponseDto>;
    private generateTokens;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<RefreshTokenResponseDto>;
    logout(userId: string): Promise<{
        message: string;
    }>;
    requestPasswordReset(requestPasswordResetDto: RequestPasswordResetDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{
        message: string;
    }>;
    setInitialPassword(setInitialPasswordDto: SetInitialPasswordDto): Promise<{
        message: string;
    }>;
    setup2FA(userId: string): Promise<Setup2FAResponseDto>;
    enable2FA(userId: string, setup2FADto: Setup2FADto): Promise<Enable2FAResponseDto>;
    disable2FA(userId: string, totpCode: string): Promise<{
        message: string;
    }>;
    regenerateRecoveryCodes(userId: string, totpCode: string): Promise<{
        recoveryCodes: string[];
    }>;
}
