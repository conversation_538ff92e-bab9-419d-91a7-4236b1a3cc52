version: '3.8'

networks:
  lgtm:
    driver: bridge

services:
  # Mimir for metrics storage (replaces Prometheus for better scalability)
  mimir:
    image: grafana/mimir:2.10.0
    container_name: mimir
    ports:
      - "9009:9009"
    volumes:
      - ./config/mimir.yml:/etc/mimir/mimir.yml
      - mimir-data:/data
    command:
      - -config.file=/etc/mimir/mimir.yml
      - -target=all
    networks:
      - lgtm
    restart: unless-stopped

  # Prometheus for scraping metrics and forwarding to Mimir
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=1h'  # Short retention, data goes to Mimir
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - lgtm
    restart: unless-stopped

  # Loki for log aggregation
  loki:
    image: grafana/loki:2.9.0
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - ./config/loki.yml:/etc/loki/local-config.yaml
      - loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - lgtm
    restart: unless-stopped

  # Tempo for distributed tracing
  tempo:
    image: grafana/tempo:2.1.1
    container_name: tempo
    ports:
      - "3200:3200"   # Tempo HTTP
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
      - "9411:9411"   # Zipkin receiver
      - "14268:14268" # Jaeger HTTP receiver
    volumes:
      - ./config/tempo.yml:/etc/tempo/tempo.yml
      - tempo-data:/var/tempo
    command:
      - -config.file=/etc/tempo/tempo.yml
    networks:
      - lgtm
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
    depends_on:
      - mimir
      - loki
      - tempo
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_FEATURE_TOGGLES_ENABLE=traceqlEditor
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel,grafana-piechart-panel
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - lgtm
    restart: unless-stopped

  # Promtail for log collection (optional, for file-based logs)
  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    volumes:
      - ./config/promtail.yml:/etc/promtail/config.yml
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - lgtm
    restart: unless-stopped

volumes:
  grafana-storage:
  mimir-data:
  prometheus-data:
  loki-data:
  tempo-data:
