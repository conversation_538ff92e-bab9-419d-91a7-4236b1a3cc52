"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateVillageResponseDto = exports.UpdateVillageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_village_dto_1 = require("./create-village.dto");
class UpdateVillageDto extends (0, swagger_1.PartialType)(create_village_dto_1.CreateVillageDto) {
}
exports.UpdateVillageDto = UpdateVillageDto;
class UpdateVillageResponseDto {
    village;
    message;
}
exports.UpdateVillageResponseDto = UpdateVillageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated village information',
    }),
    __metadata("design:type", Object)
], UpdateVillageResponseDto.prototype, "village", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Village updated successfully',
    }),
    __metadata("design:type", String)
], UpdateVillageResponseDto.prototype, "message", void 0);
//# sourceMappingURL=update-village.dto.js.map