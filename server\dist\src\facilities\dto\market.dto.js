"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateMarketResponseDto = exports.CreateMarketResponseDto = exports.MarketsListResponseDto = exports.MarketResponseDto = exports.UpdateMarketDto = exports.CreateMarketDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const base_facility_dto_1 = require("./base-facility.dto");
class CreateMarketDto extends base_facility_dto_1.BaseFacilityWithNameDto {
}
exports.CreateMarketDto = CreateMarketDto;
class UpdateMarketDto extends (0, swagger_1.PartialType)(CreateMarketDto) {
}
exports.UpdateMarketDto = UpdateMarketDto;
class MarketResponseDto extends base_facility_dto_1.BaseFacilityWithNameResponseDto {
}
exports.MarketResponseDto = MarketResponseDto;
class MarketsListResponseDto extends base_facility_dto_1.BasePaginatedResponseDto {
}
exports.MarketsListResponseDto = MarketsListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of markets',
        type: [MarketResponseDto],
    }),
    __metadata("design:type", Array)
], MarketsListResponseDto.prototype, "data", void 0);
class CreateMarketResponseDto extends base_facility_dto_1.BaseCreateResponseDto {
}
exports.CreateMarketResponseDto = CreateMarketResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created market information',
    }),
    __metadata("design:type", MarketResponseDto)
], CreateMarketResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Market created successfully',
    }),
    __metadata("design:type", String)
], CreateMarketResponseDto.prototype, "message", void 0);
class UpdateMarketResponseDto extends base_facility_dto_1.BaseUpdateResponseDto {
}
exports.UpdateMarketResponseDto = UpdateMarketResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated market information',
    }),
    __metadata("design:type", MarketResponseDto)
], UpdateMarketResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Market updated successfully',
    }),
    __metadata("design:type", String)
], UpdateMarketResponseDto.prototype, "message", void 0);
//# sourceMappingURL=market.dto.js.map