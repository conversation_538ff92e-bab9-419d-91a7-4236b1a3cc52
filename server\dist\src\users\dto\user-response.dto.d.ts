export declare class UserResponseDto {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    telephoneNumber: string;
    role: {
        id: string;
        name: string;
        privileges: string[];
    };
    accountVerified: boolean;
    is2FAEnabled: boolean;
    locations: {
        id: string;
        province?: {
            id: number;
            name: string;
        };
        district?: {
            id: number;
            name: string;
        };
        sector?: {
            id: number;
            name: string;
        };
        cell?: {
            id: number;
            name: string;
        };
        village?: {
            id: number;
            name: string;
        };
    }[];
    createdAt: Date;
    updatedAt: Date;
}
export declare class UsersListResponseDto {
    users: UserResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
