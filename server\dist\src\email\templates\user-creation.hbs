<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome! Set up your account</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #f8fafc;
            margin: 0;
            padding: 20px 0;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e7eb;
        }

        .header {
            background: #2078FF;
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 16px;
            opacity: 0.95;
            font-weight: 400;
        }

        .content {
            padding: 40px;
        }

        .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 24px;
            line-height: 1.7;
        }

        .button-container {
            text-align: center;
            margin: 32px 0;
        }

        .button {
            display: inline-block;
            background: #2078FF;
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease;
        }

        .button:hover {
            background: #1a66d9;
            transform: translateY(-1px);
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .info-box {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 12px;
            padding: 20px 24px;
            margin: 24px 0;
        }

        .info-box p {
            margin: 0;
            color: #1e40af;
            font-size: 14px;
            line-height: 1.6;
        }

        .warning-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px 24px;
            margin: 24px 0;
        }

        .warning-box p {
            margin: 0;
            color: #92400e;
            font-size: 14px;
            line-height: 1.6;
        }

        .code-block {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            word-break: break-all;
            font-size: 14px;
            margin: 24px 0;
            border: 1px solid #e2e8f0;
            box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .footer {
            background: #f8fafc;
            padding: 32px 40px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .footer p {
            margin: 8px 0;
            font-size: 14px;
            color: #6b7280;
        }

        .divider {
            height: 1px;
            background: #e5e7eb;
            margin: 24px 0;
        }

        @media only screen and (max-width: 600px) {
            body {
                padding: 10px 0;
            }

            .email-container {
                margin: 0 10px;
                border-radius: 12px;
            }

            .header, .content, .footer {
                padding: 24px 20px;
            }

            .header h1 {
                font-size: 24px;
            }

            .button {
                display: block;
                width: 100%;
                text-align: center;
                padding: 14px 24px;
            }

            .info-box, .warning-box {
                padding: 16px 20px;
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="header-content">
                <h1>WASH MIS</h1>
                <p>Water, Sanitation, and Hygiene Management Information System</p>
                <p style="font-size: 14px; margin-top: 8px; opacity: 0.9;">Ministry of Infrastructure - Rwanda</p>
            </div>
        </div>

        <div class="content">
            <div class="greeting">
                Hello {{firstName}},
            </div>

            <div class="message">
                Welcome to the WASH MIS (Water, Sanitation, and Hygiene Management Information System)! Your account has been created and you're almost ready to get started.
            </div>

            <div class="message">
                To complete your account setup, you'll need to:
                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li style="margin-bottom: 8px;">Set your initial password</li>
                    <li style="margin-bottom: 8px;">Verify your email address</li>
                    <li style="margin-bottom: 8px;">Set up two-factor authentication (2FA) for enhanced security</li>
                </ol>
            </div>

            <div class="button-container">
                <a href="{{verificationUrl}}" class="button">Set Up Your Account</a>
            </div>

            <div class="info-box">
                <p><strong>Important:</strong> This link will expire in 24 hours for security reasons. If you don't complete the setup within this time, please contact your administrator to resend the invitation.</p>
            </div>

            <div class="message">
                If you're unable to click the button above, you can copy and paste the following link into your browser:
            </div>

            <div class="code-block">
                {{verificationUrl}}
            </div>

            <div class="warning-box">
                <p><strong>Security Notice:</strong> If you did not expect to receive this email, please ignore it or contact your system administrator immediately.</p>
            </div>

            <div class="message">
                Once you've completed the setup process, you'll have full access to the WASH MIS and can begin using all available features for managing water, sanitation, and hygiene data based on your assigned role and permissions.
            </div>

            <div class="message">
                If you have any questions or need assistance during the setup process, please don't hesitate to reach out to our support team.
            </div>

            <div class="message">
                Best regards,<br>
                <strong>WASH MIS Team</strong><br>
                Ministry of Infrastructure, Rwanda
            </div>
        </div>

        <div class="footer">
            <p>This email was sent from the WASH MIS</p>
            <p>Water, Sanitation, and Hygiene Management Information System</p>
            <p style="font-weight: 600; color: #475569;">Ministry of Infrastructure, Republic of Rwanda</p>

            {{#if supportEmail}}
            <p>Need help? Contact us at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a></p>
            {{/if}}

            {{#if supportPhone}}
            <p>Phone: {{supportPhone}}</p>
            {{/if}}

            <div class="divider"></div>

            <p style="font-size: 12px; color: #94a3b8;">
                &copy; {{year}} Ministry of Infrastructure, Rwanda. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
