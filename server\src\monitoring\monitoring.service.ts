import { Injectable } from '@nestjs/common';
import { 
  authenticationAttempts,
  activeUserSessions,
  databaseQueryDuration,
  dataSubmissions,
  userRegistrations,
  facilityCount,
  locationCoverage,
  emailsSent,
  applicationErrors,
  cacheOperations
} from '../metrics';
import { createModuleLogger } from '../logger';
import { trace } from '@opentelemetry/api';

@Injectable()
export class MonitoringService {
  private readonly logger = createModuleLogger('monitoring');

  // Authentication Metrics
  recordAuthenticationAttempt(type: 'login' | '2fa' | 'refresh', status: 'success' | 'failure', method: 'local' | 'google' = 'local') {
    authenticationAttempts.inc({ type, status, method });
    
    this.logger.info('Authentication attempt recorded', {
      type,
      status,
      method,
    });

    // Add span attributes for tracing
    const span = trace.getActiveSpan();
    if (span) {
      span.setAttributes({
        'auth.type': type,
        'auth.status': status,
        'auth.method': method,
      });
    }
  }

  updateActiveUserSessions(role: string, count: number) {
    activeUserSessions.set({ role }, count);
  }

  // Database Metrics
  recordDatabaseQuery(operation: string, table: string, status: 'success' | 'failure', durationMs: number) {
    const durationSeconds = durationMs / 1000;
    databaseQueryDuration.observe({ operation, table, status }, durationSeconds);

    if (durationMs > 1000) { // Log slow queries
      this.logger.warn('Slow database query detected', {
        operation,
        table,
        status,
        duration_ms: durationMs,
      });
    }

    // Add span attributes
    const span = trace.getActiveSpan();
    if (span) {
      span.setAttributes({
        'db.operation': operation,
        'db.table': table,
        'db.status': status,
        'db.duration_ms': durationMs,
      });
    }
  }

  // Business Metrics for WASH MIS
  recordDataSubmission(facilityType: 'household' | 'school' | 'health' | 'market', locationType: string, status: 'success' | 'failure') {
    dataSubmissions.inc({ facility_type: facilityType, location_type: locationType, status });

    this.logger.info('Data submission recorded', {
      facility_type: facilityType,
      location_type: locationType,
      status,
    });

    // Add span attributes
    const span = trace.getActiveSpan();
    if (span) {
      span.setAttributes({
        'wash_mis.facility_type': facilityType,
        'wash_mis.location_type': locationType,
        'wash_mis.submission_status': status,
      });
    }
  }

  recordUserRegistration(role: string, status: 'success' | 'failure') {
    userRegistrations.inc({ role, status });

    this.logger.info('User registration recorded', {
      role,
      status,
    });
  }

  updateFacilityCount(type: string, province: string, district: string, count: number) {
    facilityCount.set({ type, province, district }, count);
  }

  updateLocationCoverage(province: string, district: string, sector: string, metricType: string, value: number) {
    locationCoverage.set({ province, district, sector, metric_type: metricType }, value);
  }

  // Email Metrics
  recordEmailSent(type: 'verification' | 'password_reset' | '2fa_setup' | 'notification', status: 'success' | 'failure') {
    emailsSent.inc({ type, status });

    this.logger.info('Email sent recorded', {
      type,
      status,
    });
  }

  // Error Metrics
  recordApplicationError(type: 'validation' | 'database' | 'auth' | 'business', severity: 'low' | 'medium' | 'high' | 'critical', module: string, error?: Error) {
    applicationErrors.inc({ type, severity, module });

    this.logger.error('Application error recorded', {
      type,
      severity,
      module,
      error_message: error?.message,
      error_stack: error?.stack,
    });

    // Add span attributes and mark as error
    const span = trace.getActiveSpan();
    if (span) {
      span.setAttributes({
        'error.type': type,
        'error.severity': severity,
        'error.module': module,
        'error.message': error?.message || 'Unknown error',
      });
      span.recordException(error || new Error('Unknown error'));
    }
  }

  // Cache Metrics
  recordCacheOperation(operation: 'get' | 'set' | 'delete', status: 'hit' | 'miss' | 'success' | 'failure') {
    cacheOperations.inc({ operation, status });
  }

  // Helper method to create custom spans for business operations
  createBusinessSpan(operationName: string, attributes?: Record<string, string | number>) {
    const tracer = trace.getTracer('wash-mis-business');
    const span = tracer.startSpan(operationName);
    
    if (attributes) {
      span.setAttributes(attributes);
    }

    return span;
  }

  // Method to track business operation duration
  async trackBusinessOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    attributes?: Record<string, string | number>
  ): Promise<T> {
    const span = this.createBusinessSpan(operationName, attributes);
    const startTime = Date.now();

    try {
      const result = await operation();
      span.setStatus({ code: 1 }); // OK
      return result;
    } catch (error) {
      span.setStatus({ code: 2, message: error.message }); // ERROR
      span.recordException(error);
      this.recordApplicationError('business', 'high', operationName, error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      span.setAttributes({ 'operation.duration_ms': duration });
      span.end();

      this.logger.info('Business operation completed', {
        operation: operationName,
        duration_ms: duration,
        ...attributes,
      });
    }
  }
}
