"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MobileGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const client_1 = require("@prisma/client");
let MobileGuard = class MobileGuard {
    reflector;
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.role || !user.role.privileges) {
            throw new common_1.ForbiddenException('Access denied: No user information found');
        }
        const userPrivileges = user.role.privileges;
        const isMobileOnlyUser = userPrivileges.includes(client_1.Privilege.DATA_COLLECTION) &&
            userPrivileges.length === 1;
        if (isMobileOnlyUser) {
            throw new common_1.ForbiddenException('Access denied: Mobile-only users cannot access web application endpoints');
        }
        return true;
    }
};
exports.MobileGuard = MobileGuard;
exports.MobileGuard = MobileGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], MobileGuard);
//# sourceMappingURL=mobile.guard.js.map