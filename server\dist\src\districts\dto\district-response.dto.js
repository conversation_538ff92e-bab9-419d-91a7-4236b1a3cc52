"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistrictsListResponseDto = exports.DistrictResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class DistrictResponseDto {
    id;
    code;
    name;
    province;
    sectorCount;
    createdAt;
    updatedAt;
}
exports.DistrictResponseDto = DistrictResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District ID',
        example: 1,
    }),
    __metadata("design:type", Number)
], DistrictResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District code',
        example: 101,
    }),
    __metadata("design:type", Number)
], DistrictResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District name',
        example: 'Nyarugenge',
    }),
    __metadata("design:type", String)
], DistrictResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Province information',
    }),
    __metadata("design:type", Object)
], DistrictResponseDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of sectors in this district',
        example: 10,
    }),
    __metadata("design:type", Number)
], DistrictResponseDto.prototype, "sectorCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], DistrictResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'District last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], DistrictResponseDto.prototype, "updatedAt", void 0);
class DistrictsListResponseDto {
    districts;
    total;
    page;
    limit;
    totalPages;
}
exports.DistrictsListResponseDto = DistrictsListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of districts',
        type: [DistrictResponseDto],
    }),
    __metadata("design:type", Array)
], DistrictsListResponseDto.prototype, "districts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of districts',
        example: 30,
    }),
    __metadata("design:type", Number)
], DistrictsListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], DistrictsListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], DistrictsListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 3,
    }),
    __metadata("design:type", Number)
], DistrictsListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=district-response.dto.js.map