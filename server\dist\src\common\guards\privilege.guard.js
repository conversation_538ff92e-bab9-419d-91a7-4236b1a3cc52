"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrivilegeGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
let PrivilegeGuard = class PrivilegeGuard {
    reflector;
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredPrivileges = this.reflector.getAllAndOverride('privileges', [context.getHandler(), context.getClass()]);
        if (!requiredPrivileges) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.role || !user.role.privileges) {
            throw new common_1.ForbiddenException('Access denied: No privileges found');
        }
        const userPrivileges = user.role.privileges;
        const hasPrivilege = requiredPrivileges.some((privilege) => userPrivileges.includes(privilege));
        if (!hasPrivilege) {
            throw new common_1.ForbiddenException(`Access denied: Required privileges: ${requiredPrivileges.join(', ')}`);
        }
        return true;
    }
};
exports.PrivilegeGuard = PrivilegeGuard;
exports.PrivilegeGuard = PrivilegeGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], PrivilegeGuard);
//# sourceMappingURL=privilege.guard.js.map