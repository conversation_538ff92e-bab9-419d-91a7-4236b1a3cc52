import { BaseFacilityWithNameDto, BaseFacilityWithNameResponseDto, BasePaginatedResponseDto, BaseCreateResponseDto, BaseUpdateResponseDto } from './base-facility.dto';
export declare class CreateHealthFacilityDto extends BaseFacilityWithNameDto {
}
declare const UpdateHealthFacilityDto_base: import("@nestjs/common").Type<Partial<CreateHealthFacilityDto>>;
export declare class UpdateHealthFacilityDto extends UpdateHealthFacilityDto_base {
}
export declare class HealthFacilityResponseDto extends BaseFacilityWithNameResponseDto {
}
export declare class HealthFacilitiesListResponseDto extends BasePaginatedResponseDto<HealthFacilityResponseDto> {
    data: HealthFacilityResponseDto[];
}
export declare class CreateHealthFacilityResponseDto extends BaseCreateResponseDto<HealthFacilityResponseDto> {
    facility: HealthFacilityResponseDto;
    message: string;
}
export declare class UpdateHealthFacilityResponseDto extends BaseUpdateResponseDto<HealthFacilityResponseDto> {
    facility: HealthFacilityResponseDto;
    message: string;
}
export {};
