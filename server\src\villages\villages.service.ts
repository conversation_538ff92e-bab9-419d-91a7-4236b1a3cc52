import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateVillageDto,
  CreateVillageResponseDto,
} from './dto/create-village.dto';
import {
  UpdateVillageDto,
  UpdateVillageResponseDto,
} from './dto/update-village.dto';
import {
  VillageResponseDto,
  VillagesListResponseDto,
} from './dto/village-response.dto';

@Injectable()
export class VillagesService {
  constructor(private prisma: PrismaService) {}

  async create(createVillageDto: CreateVillageDto): Promise<CreateVillageResponseDto> {
    const { code, name, cellId } = createVillageDto;

    // Check if cell exists
    const cell = await this.prisma.cell.findUnique({
      where: { id: cellId },
    });

    if (!cell) {
      throw new NotFoundException('Cell not found');
    }

    // Check if village with same code or name already exists
    const existingVillage = await this.prisma.village.findFirst({
      where: {
        OR: [
          { code },
          { name },
        ],
      },
    });

    if (existingVillage) {
      if (existingVillage.code === code) {
        throw new ConflictException('Village with this code already exists');
      }
      if (existingVillage.name === name) {
        throw new ConflictException('Village with this name already exists');
      }
    }

    // Create village
    const village = await this.prisma.village.create({
      data: {
        code,
        name,
        cellId,
      },
    });

    return {
      village: {
        id: village.id,
        code: village.code,
        name: village.name,
        cellId: village.cellId,
        createdAt: village.createdAt,
      },
      message: 'Village created successfully',
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    cellId?: number,
  ): Promise<VillagesListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      AND: [
        search
          ? {
              OR: [
                { name: { contains: search, mode: 'insensitive' as const } },
                { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
              ].filter(Boolean),
            }
          : {},
        cellId ? { cellId } : {},
      ],
    };

    const [villages, total] = await Promise.all([
      this.prisma.village.findMany({
        where,
        skip,
        take: limit,
        include: {
          cell: {
            include: {
              sector: {
                include: {
                  district: {
                    include: {
                      province: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          code: 'asc',
        },
      }),
      this.prisma.village.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      villages: villages.map((village) => ({
        id: village.id,
        code: village.code,
        name: village.name,
        cell: {
          id: village.cell.id,
          name: village.cell.name,
          code: village.cell.code,
          sector: {
            id: village.cell.sector.id,
            name: village.cell.sector.name,
            code: village.cell.sector.code,
            district: {
              id: village.cell.sector.district.id,
              name: village.cell.sector.district.name,
              code: village.cell.sector.district.code,
              province: {
                id: village.cell.sector.district.province.id,
                name: village.cell.sector.district.province.name,
                code: village.cell.sector.district.province.code,
              },
            },
          },
        },
        createdAt: village.createdAt,
        updatedAt: village.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: number): Promise<VillageResponseDto> {
    const village = await this.prisma.village.findUnique({
      where: { id },
      include: {
        cell: {
          include: {
            sector: {
              include: {
                district: {
                  include: {
                    province: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!village) {
      throw new NotFoundException('Village not found');
    }

    return {
      id: village.id,
      code: village.code,
      name: village.name,
      cell: {
        id: village.cell.id,
        name: village.cell.name,
        code: village.cell.code,
        sector: {
          id: village.cell.sector.id,
          name: village.cell.sector.name,
          code: village.cell.sector.code,
          district: {
            id: village.cell.sector.district.id,
            name: village.cell.sector.district.name,
            code: village.cell.sector.district.code,
            province: {
              id: village.cell.sector.district.province.id,
              name: village.cell.sector.district.province.name,
              code: village.cell.sector.district.province.code,
            },
          },
        },
      },
      createdAt: village.createdAt,
      updatedAt: village.updatedAt,
    };
  }

  async update(id: number, updateVillageDto: UpdateVillageDto): Promise<UpdateVillageResponseDto> {
    const { code, name, cellId } = updateVillageDto;

    // Check if village exists
    const existingVillage = await this.prisma.village.findUnique({
      where: { id },
    });

    if (!existingVillage) {
      throw new NotFoundException('Village not found');
    }

    // Check if cell exists if provided
    if (cellId) {
      const cell = await this.prisma.cell.findUnique({
        where: { id: cellId },
      });

      if (!cell) {
        throw new NotFoundException('Cell not found');
      }
    }

    // Check for conflicts with other villages
    if (code || name) {
      const conflictVillage = await this.prisma.village.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                code ? { code } : {},
                name ? { name } : {},
              ].filter(Boolean),
            },
          ],
        },
      });

      if (conflictVillage) {
        if (conflictVillage.code === code) {
          throw new ConflictException('Village with this code already exists');
        }
        if (conflictVillage.name === name) {
          throw new ConflictException('Village with this name already exists');
        }
      }
    }

    // Update village
    const updatedVillage = await this.prisma.village.update({
      where: { id },
      data: {
        ...(code && { code }),
        ...(name && { name }),
        ...(cellId && { cellId }),
      },
    });

    return {
      village: {
        id: updatedVillage.id,
        code: updatedVillage.code,
        name: updatedVillage.name,
        cellId: updatedVillage.cellId,
        updatedAt: updatedVillage.updatedAt,
      },
      message: 'Village updated successfully',
    };
  }

  async remove(id: number): Promise<{ message: string }> {
    // Check if village exists
    const existingVillage = await this.prisma.village.findUnique({
      where: { id },
    });

    if (!existingVillage) {
      throw new NotFoundException('Village not found');
    }

    // Delete village
    await this.prisma.village.delete({
      where: { id },
    });

    return { message: 'Village deleted successfully' };
  }
}
