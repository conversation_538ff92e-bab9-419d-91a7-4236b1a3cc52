export declare class MonitoringService {
    private readonly logger;
    recordAuthenticationAttempt(type: 'login' | '2fa' | 'refresh', status: 'success' | 'failure', method?: 'local' | 'google'): void;
    updateActiveUserSessions(role: string, count: number): void;
    recordDatabaseQuery(operation: string, table: string, status: 'success' | 'failure', durationMs: number): void;
    recordDataSubmission(facilityType: 'household' | 'school' | 'health' | 'market', locationType: string, status: 'success' | 'failure'): void;
    recordUserRegistration(role: string, status: 'success' | 'failure'): void;
    updateFacilityCount(type: string, province: string, district: string, count: number): void;
    updateLocationCoverage(province: string, district: string, sector: string, metricType: string, value: number): void;
    recordEmailSent(type: 'verification' | 'password_reset' | '2fa_setup' | 'notification', status: 'success' | 'failure'): void;
    recordApplicationError(type: 'validation' | 'database' | 'auth' | 'business', severity: 'low' | 'medium' | 'high' | 'critical', module: string, error?: Error): void;
    recordCacheOperation(operation: 'get' | 'set' | 'delete', status: 'hit' | 'miss' | 'success' | 'failure'): void;
    createBusinessSpan(operationName: string, attributes?: Record<string, string | number>): import("@opentelemetry/api").Span;
    trackBusinessOperation<T>(operationName: string, operation: () => Promise<T>, attributes?: Record<string, string | number>): Promise<T>;
}
