import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateProvinceDto,
  CreateProvinceResponseDto,
} from './dto/create-province.dto';
import {
  UpdateProvinceDto,
  UpdateProvinceResponseDto,
} from './dto/update-province.dto';
import {
  ProvinceResponseDto,
  ProvincesListResponseDto,
} from './dto/province-response.dto';

@Injectable()
export class ProvincesService {
  constructor(private prisma: PrismaService) {}

  async create(createProvinceDto: CreateProvinceDto): Promise<CreateProvinceResponseDto> {
    const { code, name } = createProvinceDto;

    // Check if province with same code or name already exists
    const existingProvince = await this.prisma.province.findFirst({
      where: {
        OR: [
          { code },
          { name },
        ],
      },
    });

    if (existingProvince) {
      if (existingProvince.code === code) {
        throw new ConflictException('Province with this code already exists');
      }
      if (existingProvince.name === name) {
        throw new ConflictException('Province with this name already exists');
      }
    }

    // Create province
    const province = await this.prisma.province.create({
      data: {
        code,
        name,
      },
    });

    return {
      province: {
        id: province.id,
        code: province.code,
        name: province.name,
        createdAt: province.createdAt,
      },
      message: 'Province created successfully',
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
  ): Promise<ProvincesListResponseDto> {
    const skip = (page - 1) * limit;

    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
          ].filter(Boolean),
        }
      : {};

    const [provinces, total] = await Promise.all([
      this.prisma.province.findMany({
        where,
        skip,
        take: limit,
        include: {
          _count: {
            select: { districts: true },
          },
        },
        orderBy: {
          code: 'asc',
        },
      }),
      this.prisma.province.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      provinces: provinces.map((province) => ({
        id: province.id,
        code: province.code,
        name: province.name,
        districtCount: province._count.districts,
        createdAt: province.createdAt,
        updatedAt: province.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: number): Promise<ProvinceResponseDto> {
    const province = await this.prisma.province.findUnique({
      where: { id },
      include: {
        _count: {
          select: { districts: true },
        },
      },
    });

    if (!province) {
      throw new NotFoundException('Province not found');
    }

    return {
      id: province.id,
      code: province.code,
      name: province.name,
      districtCount: province._count.districts,
      createdAt: province.createdAt,
      updatedAt: province.updatedAt,
    };
  }

  async update(id: number, updateProvinceDto: UpdateProvinceDto): Promise<UpdateProvinceResponseDto> {
    const { code, name } = updateProvinceDto;

    // Check if province exists
    const existingProvince = await this.prisma.province.findUnique({
      where: { id },
    });

    if (!existingProvince) {
      throw new NotFoundException('Province not found');
    }

    // Check for conflicts with other provinces
    if (code || name) {
      const conflictProvince = await this.prisma.province.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                code ? { code } : {},
                name ? { name } : {},
              ].filter(Boolean),
            },
          ],
        },
      });

      if (conflictProvince) {
        if (conflictProvince.code === code) {
          throw new ConflictException('Province with this code already exists');
        }
        if (conflictProvince.name === name) {
          throw new ConflictException('Province with this name already exists');
        }
      }
    }

    // Update province
    const updatedProvince = await this.prisma.province.update({
      where: { id },
      data: {
        ...(code && { code }),
        ...(name && { name }),
      },
    });

    return {
      province: {
        id: updatedProvince.id,
        code: updatedProvince.code,
        name: updatedProvince.name,
        updatedAt: updatedProvince.updatedAt,
      },
      message: 'Province updated successfully',
    };
  }

  async remove(id: number): Promise<{ message: string }> {
    // Check if province exists
    const existingProvince = await this.prisma.province.findUnique({
      where: { id },
      include: {
        _count: {
          select: { districts: true },
        },
      },
    });

    if (!existingProvince) {
      throw new NotFoundException('Province not found');
    }

    // Check if province has districts
    if (existingProvince._count.districts > 0) {
      throw new BadRequestException(
        `Cannot delete province. ${existingProvince._count.districts} district(s) belong to this province`,
      );
    }

    // Delete province
    await this.prisma.province.delete({
      where: { id },
    });

    return { message: 'Province deleted successfully' };
  }
}
