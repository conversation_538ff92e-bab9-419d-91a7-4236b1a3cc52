import { PrismaService } from '../prisma/prisma.service';
import { CreateVillageDto, CreateVillageResponseDto } from './dto/create-village.dto';
import { UpdateVillageDto, UpdateVillageResponseDto } from './dto/update-village.dto';
import { VillageResponseDto, VillagesListResponseDto } from './dto/village-response.dto';
export declare class VillagesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createVillageDto: CreateVillageDto): Promise<CreateVillageResponseDto>;
    findAll(page?: number, limit?: number, search?: string, cellId?: number): Promise<VillagesListResponseDto>;
    findOne(id: number): Promise<VillageResponseDto>;
    update(id: number, updateVillageDto: UpdateVillageDto): Promise<UpdateVillageResponseDto>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
