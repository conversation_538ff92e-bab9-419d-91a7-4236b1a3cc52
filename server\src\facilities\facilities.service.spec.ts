import { Test, TestingModule } from '@nestjs/testing';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { FacilitiesService } from './facilities.service';
import { PrismaService } from '../prisma/prisma.service';

describe('FacilitiesService', () => {
  let service: FacilitiesService;
  let prisma: PrismaService;

  const mockPrismaService = {
    houseHold: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    school: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    healthFacility: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    market: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    location: {
      findUnique: jest.fn(),
    },
  };

  const mockLocation = {
    id: 'location_123',
    villageId: 1,
    village: {
      id: 1,
      name: 'Test Village',
      cell: {
        id: 1,
        name: 'Test Cell',
        sector: {
          id: 1,
          name: 'Test Sector',
          district: {
            id: 1,
            name: 'Test District',
            province: {
              id: 1,
              name: 'Test Province',
            },
          },
        },
      },
    },
    latitude: -1.9441,
    longitude: 30.0619,
    settlementType: 'URBAN',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FacilitiesService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<FacilitiesService>(FacilitiesService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Household Operations', () => {
    const mockHousehold = {
      id: 'household_123',
      number: 1001,
      locationId: 'location_123',
      location: mockLocation,
      createdAt: new Date(),
      updatedAt: new Date(),
      deleted: false,
    };

    describe('createHousehold', () => {
      it('should create a household successfully', async () => {
        const createDto = {
          locationId: 'location_123',
          number: 1001,
        };

        mockPrismaService.houseHold.findUnique.mockResolvedValue(null);
        mockPrismaService.location.findUnique.mockResolvedValue(mockLocation);
        mockPrismaService.houseHold.create.mockResolvedValue(mockHousehold);

        const result = await service.createHousehold(createDto);

        expect(result.message).toBe('Household created successfully');
        expect(result.facility.id).toBe('household_123');
        expect(result.facility.number).toBe(1001);
      });

      it('should throw ConflictException if household number exists', async () => {
        const createDto = {
          locationId: 'location_123',
          number: 1001,
        };

        mockPrismaService.houseHold.findUnique.mockResolvedValue(mockHousehold);

        await expect(service.createHousehold(createDto)).rejects.toThrow(
          ConflictException,
        );
      });

      it('should throw NotFoundException if location does not exist', async () => {
        const createDto = {
          locationId: 'location_123',
          number: 1001,
        };

        mockPrismaService.houseHold.findUnique.mockResolvedValue(null);
        mockPrismaService.location.findUnique.mockResolvedValue(null);

        await expect(service.createHousehold(createDto)).rejects.toThrow(
          NotFoundException,
        );
      });
    });

    describe('findAllHouseholds', () => {
      it('should return paginated households', async () => {
        const households = [mockHousehold];
        mockPrismaService.houseHold.findMany.mockResolvedValue(households);
        mockPrismaService.houseHold.count.mockResolvedValue(1);

        const result = await service.findAllHouseholds(1, 10);

        expect(result.data).toHaveLength(1);
        expect(result.total).toBe(1);
        expect(result.page).toBe(1);
        expect(result.limit).toBe(10);
        expect(result.totalPages).toBe(1);
      });
    });

    describe('findOneHousehold', () => {
      it('should return a household by ID', async () => {
        mockPrismaService.houseHold.findFirst.mockResolvedValue(mockHousehold);

        const result = await service.findOneHousehold('household_123');

        expect(result.id).toBe('household_123');
        expect(result.number).toBe(1001);
      });

      it('should throw NotFoundException if household not found', async () => {
        mockPrismaService.houseHold.findFirst.mockResolvedValue(null);

        await expect(service.findOneHousehold('household_123')).rejects.toThrow(
          NotFoundException,
        );
      });
    });

    describe('updateHousehold', () => {
      it('should update a household successfully', async () => {
        const updateDto = { number: 1002 };
        const updatedHousehold = { ...mockHousehold, number: 1002 };

        mockPrismaService.houseHold.findFirst.mockResolvedValue(mockHousehold);
        mockPrismaService.houseHold.findUnique.mockResolvedValue(null);
        mockPrismaService.houseHold.update.mockResolvedValue(updatedHousehold);

        const result = await service.updateHousehold('household_123', updateDto);

        expect(result.message).toBe('Household updated successfully');
        expect(result.facility.number).toBe(1002);
      });

      it('should throw NotFoundException if household not found', async () => {
        const updateDto = { number: 1002 };
        mockPrismaService.houseHold.findFirst.mockResolvedValue(null);

        await expect(
          service.updateHousehold('household_123', updateDto),
        ).rejects.toThrow(NotFoundException);
      });
    });

    describe('removeHousehold', () => {
      it('should soft delete a household successfully', async () => {
        mockPrismaService.houseHold.findFirst.mockResolvedValue(mockHousehold);
        mockPrismaService.houseHold.update.mockResolvedValue({
          ...mockHousehold,
          deleted: true,
        });

        const result = await service.removeHousehold('household_123');

        expect(result.message).toBe('Household deleted successfully');
        expect(mockPrismaService.houseHold.update).toHaveBeenCalledWith({
          where: { id: 'household_123' },
          data: { deleted: true },
        });
      });

      it('should throw NotFoundException if household not found', async () => {
        mockPrismaService.houseHold.findFirst.mockResolvedValue(null);

        await expect(service.removeHousehold('household_123')).rejects.toThrow(
          NotFoundException,
        );
      });
    });
  });

  describe('School Operations', () => {
    const mockSchool = {
      id: 'school_123',
      number: 2001,
      name: 'Test Primary School',
      locationId: 'location_123',
      location: mockLocation,
      createdAt: new Date(),
      updatedAt: new Date(),
      deleted: false,
    };

    describe('createSchool', () => {
      it('should create a school successfully', async () => {
        const createDto = {
          locationId: 'location_123',
          number: 2001,
          name: 'Test Primary School',
        };

        mockPrismaService.school.findUnique.mockResolvedValue(null);
        mockPrismaService.location.findUnique.mockResolvedValue(mockLocation);
        mockPrismaService.school.create.mockResolvedValue(mockSchool);

        const result = await service.createSchool(createDto);

        expect(result.message).toBe('School created successfully');
        expect(result.facility.id).toBe('school_123');
        expect(result.facility.name).toBe('Test Primary School');
      });
    });

    describe('findAllSchools', () => {
      it('should return paginated schools with search', async () => {
        const schools = [mockSchool];
        mockPrismaService.school.findMany.mockResolvedValue(schools);
        mockPrismaService.school.count.mockResolvedValue(1);

        const result = await service.findAllSchools(1, 10, 'Primary');

        expect(result.data).toHaveLength(1);
        expect(result.total).toBe(1);
      });
    });
  });
});
