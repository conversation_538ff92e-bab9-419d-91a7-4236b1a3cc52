import { PrismaService } from '../prisma/prisma.service';
import { CreateCellDto, CreateCellResponseDto } from './dto/create-cell.dto';
import { UpdateCellDto, UpdateCellResponseDto } from './dto/update-cell.dto';
import { CellResponseDto, CellsListResponseDto } from './dto/cell-response.dto';
export declare class CellsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createCellDto: CreateCellDto): Promise<CreateCellResponseDto>;
    findAll(page?: number, limit?: number, search?: string, sectorId?: number): Promise<CellsListResponseDto>;
    findOne(id: number): Promise<CellResponseDto>;
    update(id: number, updateCellDto: UpdateCellDto): Promise<UpdateCellResponseDto>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
