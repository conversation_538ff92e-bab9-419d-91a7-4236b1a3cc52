import { VillagesService } from './villages.service';
import { CreateVillageDto, CreateVillageResponseDto } from './dto/create-village.dto';
import { UpdateVillageDto, UpdateVillageResponseDto } from './dto/update-village.dto';
import { VillageResponseDto, VillagesListResponseDto } from './dto/village-response.dto';
export declare class VillagesController {
    private readonly villagesService;
    constructor(villagesService: VillagesService);
    create(createVillageDto: CreateVillageDto): Promise<CreateVillageResponseDto>;
    findAll(page?: number, limit?: number, search?: string, cellId?: number): Promise<VillagesListResponseDto>;
    findOne(id: number): Promise<VillageResponseDto>;
    update(id: number, updateVillageDto: UpdateVillageDto): Promise<UpdateVillageResponseDto>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
