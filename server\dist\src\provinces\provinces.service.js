"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProvincesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ProvincesService = class ProvincesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createProvinceDto) {
        const { code, name } = createProvinceDto;
        const existingProvince = await this.prisma.province.findFirst({
            where: {
                OR: [
                    { code },
                    { name },
                ],
            },
        });
        if (existingProvince) {
            if (existingProvince.code === code) {
                throw new common_1.ConflictException('Province with this code already exists');
            }
            if (existingProvince.name === name) {
                throw new common_1.ConflictException('Province with this name already exists');
            }
        }
        const province = await this.prisma.province.create({
            data: {
                code,
                name,
            },
        });
        return {
            province: {
                id: province.id,
                code: province.code,
                name: province.name,
                createdAt: province.createdAt,
            },
            message: 'Province created successfully',
        };
    }
    async findAll(page = 1, limit = 10, search) {
        const skip = (page - 1) * limit;
        const where = search
            ? {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
                ].filter(Boolean),
            }
            : {};
        const [provinces, total] = await Promise.all([
            this.prisma.province.findMany({
                where,
                skip,
                take: limit,
                include: {
                    _count: {
                        select: { districts: true },
                    },
                },
                orderBy: {
                    code: 'asc',
                },
            }),
            this.prisma.province.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            provinces: provinces.map((province) => ({
                id: province.id,
                code: province.code,
                name: province.name,
                districtCount: province._count.districts,
                createdAt: province.createdAt,
                updatedAt: province.updatedAt,
            })),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const province = await this.prisma.province.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { districts: true },
                },
            },
        });
        if (!province) {
            throw new common_1.NotFoundException('Province not found');
        }
        return {
            id: province.id,
            code: province.code,
            name: province.name,
            districtCount: province._count.districts,
            createdAt: province.createdAt,
            updatedAt: province.updatedAt,
        };
    }
    async update(id, updateProvinceDto) {
        const { code, name } = updateProvinceDto;
        const existingProvince = await this.prisma.province.findUnique({
            where: { id },
        });
        if (!existingProvince) {
            throw new common_1.NotFoundException('Province not found');
        }
        if (code || name) {
            const conflictProvince = await this.prisma.province.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        {
                            OR: [
                                code ? { code } : {},
                                name ? { name } : {},
                            ].filter(Boolean),
                        },
                    ],
                },
            });
            if (conflictProvince) {
                if (conflictProvince.code === code) {
                    throw new common_1.ConflictException('Province with this code already exists');
                }
                if (conflictProvince.name === name) {
                    throw new common_1.ConflictException('Province with this name already exists');
                }
            }
        }
        const updatedProvince = await this.prisma.province.update({
            where: { id },
            data: {
                ...(code && { code }),
                ...(name && { name }),
            },
        });
        return {
            province: {
                id: updatedProvince.id,
                code: updatedProvince.code,
                name: updatedProvince.name,
                updatedAt: updatedProvince.updatedAt,
            },
            message: 'Province updated successfully',
        };
    }
    async remove(id) {
        const existingProvince = await this.prisma.province.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { districts: true },
                },
            },
        });
        if (!existingProvince) {
            throw new common_1.NotFoundException('Province not found');
        }
        if (existingProvince._count.districts > 0) {
            throw new common_1.BadRequestException(`Cannot delete province. ${existingProvince._count.districts} district(s) belong to this province`);
        }
        await this.prisma.province.delete({
            where: { id },
        });
        return { message: 'Province deleted successfully' };
    }
};
exports.ProvincesService = ProvincesService;
exports.ProvincesService = ProvincesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ProvincesService);
//# sourceMappingURL=provinces.service.js.map