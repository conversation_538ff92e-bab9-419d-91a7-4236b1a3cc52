import { ApiProperty } from '@nestjs/swagger';

export class ProvinceResponseDto {
  @ApiProperty({
    description: 'Province ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Province code',
    example: 1,
  })
  code: number;

  @ApiProperty({
    description: 'Province name',
    example: 'Kigali City',
  })
  name: string;

  @ApiProperty({
    description: 'Number of districts in this province',
    example: 3,
  })
  districtCount: number;

  @ApiProperty({
    description: 'Province creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Province last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class ProvincesListResponseDto {
  @ApiProperty({
    description: 'List of provinces',
    type: [ProvinceResponseDto],
  })
  provinces: ProvinceResponseDto[];

  @ApiProperty({
    description: 'Total number of provinces',
    example: 5,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 1,
  })
  totalPages: number;
}
