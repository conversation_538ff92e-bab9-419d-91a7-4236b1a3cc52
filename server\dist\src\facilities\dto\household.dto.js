"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateHouseholdResponseDto = exports.CreateHouseholdResponseDto = exports.HouseholdsListResponseDto = exports.HouseholdResponseDto = exports.UpdateHouseholdDto = exports.CreateHouseholdDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const base_facility_dto_1 = require("./base-facility.dto");
class CreateHouseholdDto extends base_facility_dto_1.BaseFacilityDto {
}
exports.CreateHouseholdDto = CreateHouseholdDto;
class UpdateHouseholdDto extends (0, swagger_1.PartialType)(CreateHouseholdDto) {
}
exports.UpdateHouseholdDto = UpdateHouseholdDto;
class HouseholdResponseDto extends base_facility_dto_1.BaseFacilityResponseDto {
}
exports.HouseholdResponseDto = HouseholdResponseDto;
class HouseholdsListResponseDto extends base_facility_dto_1.BasePaginatedResponseDto {
    data;
}
exports.HouseholdsListResponseDto = HouseholdsListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of households',
        type: [HouseholdResponseDto],
    }),
    __metadata("design:type", Array)
], HouseholdsListResponseDto.prototype, "data", void 0);
class CreateHouseholdResponseDto extends base_facility_dto_1.BaseCreateResponseDto {
    facility;
    message;
}
exports.CreateHouseholdResponseDto = CreateHouseholdResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created household information',
    }),
    __metadata("design:type", HouseholdResponseDto)
], CreateHouseholdResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Household created successfully',
    }),
    __metadata("design:type", String)
], CreateHouseholdResponseDto.prototype, "message", void 0);
class UpdateHouseholdResponseDto extends base_facility_dto_1.BaseUpdateResponseDto {
    facility;
    message;
}
exports.UpdateHouseholdResponseDto = UpdateHouseholdResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated household information',
    }),
    __metadata("design:type", HouseholdResponseDto)
], UpdateHouseholdResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Household updated successfully',
    }),
    __metadata("design:type", String)
], UpdateHouseholdResponseDto.prototype, "message", void 0);
//# sourceMappingURL=household.dto.js.map