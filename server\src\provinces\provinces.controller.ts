import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { ProvincesService } from './provinces.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { PrivilegeGuard } from '../common/guards/privilege.guard';
import { MobileGuard } from '../common/guards/mobile.guard';
import { Privileges } from '../common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';
import {
  CreateProvinceDto,
  CreateProvinceResponseDto,
} from './dto/create-province.dto';
import {
  UpdateProvinceDto,
  UpdateProvinceResponseDto,
} from './dto/update-province.dto';
import {
  ProvinceResponseDto,
  ProvincesListResponseDto,
} from './dto/province-response.dto';

@ApiTags('Provinces')
@Controller('provinces')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
export class ProvincesController {
  constructor(private readonly provincesService: ProvincesService) {}

  @Post()
  @Privileges(Privilege.USER_MANAGEMENT)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new province' })
  @ApiResponse({
    status: 201,
    description: 'Province created successfully',
    type: CreateProvinceResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 409, description: 'Province already exists' })
  async create(@Body() createProvinceDto: CreateProvinceDto) {
    return this.provincesService.create(createProvinceDto);
  }

  @Get()
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get all provinces with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by province name or code',
    example: 'Kigali',
  })
  @ApiResponse({
    status: 200,
    description: 'Provinces retrieved successfully',
    type: ProvincesListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('search') search?: string,
  ) {
    return this.provincesService.findAll(page, limit, search);
  }

  @Get(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get a province by ID' })
  @ApiParam({
    name: 'id',
    description: 'Province ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Province retrieved successfully',
    type: ProvinceResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Province not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.provincesService.findOne(id);
  }

  @Patch(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Update a province' })
  @ApiParam({
    name: 'id',
    description: 'Province ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Province updated successfully',
    type: UpdateProvinceResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Province not found' })
  @ApiResponse({ status: 409, description: 'Province already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateProvinceDto: UpdateProvinceDto) {
    return this.provincesService.update(id, updateProvinceDto);
  }

  @Delete(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Delete a province' })
  @ApiParam({
    name: 'id',
    description: 'Province ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Province deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Province deleted successfully',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - Province has districts' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Province not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.provincesService.remove(id);
  }
}
