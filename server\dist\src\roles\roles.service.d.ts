import { PrismaService } from '../prisma/prisma.service';
import { CreateRoleDto, CreateRoleResponseDto } from './dto/create-role.dto';
import { UpdateRoleDto, UpdateRoleResponseDto } from './dto/update-role.dto';
import { RoleResponseDto, RolesListResponseDto } from './dto/role-response.dto';
export declare class RolesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createRoleDto: CreateRoleDto): Promise<CreateRoleResponseDto>;
    findAll(page?: number, limit?: number, search?: string): Promise<RolesListResponseDto>;
    findOne(id: string): Promise<RoleResponseDto>;
    update(id: string, updateRoleDto: UpdateRoleDto): Promise<UpdateRoleResponseDto>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
