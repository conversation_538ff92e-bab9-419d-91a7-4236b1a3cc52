import { PrismaClient, Privilege } from "@prisma/client";
import fs from 'fs/promises';
import path from 'path';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

const readJSON = async (fileName: string) => {
  const filePath = path.join(__dirname, 'data', fileName);
  const data = await fs.readFile(filePath, 'utf-8');
  return JSON.parse(data);
};

const seedLocations = async () => {
  const provinces = await readJSON('provinces.json');
  const districts = await readJSON('districts.json');
  const sectors = await readJSON('sectors.json');
  const cells = await readJSON('cells.json');
  const villages = await readJSON('villages.json');

  console.log('Upserting provinces...');
  for (const province of provinces) {
    await prisma.province.upsert({
      where: { id: province.id },
      update: { name: province.name },
      create: {
        id: province.id,
        code: province.id,
        name: province.name,
      },
    });
  }

  console.log('Upserting districts...');
  for (const district of districts) {
    await prisma.district.upsert({
      where: { id: district.id },
      update: {
        name: district.name,
        provinceId: district.province_id,
      },
      create: {
        id: district.id,
        code: district.id,
        name: district.name,
        provinceId: district.province_id,
      },
    });
  }

  console.log('Upserting sectors...');
  for (const sector of sectors) {
    await prisma.sector.upsert({
      where: { id: sector.id },
      update: {
        name: sector.name,
        districtId: sector.district_id,
      },
      create: {
        id: sector.id,
        code: sector.id,
        name: sector.name,
        districtId: sector.district_id,
      },
    });
  }

  console.log('Upserting cells...');
  for (const cell of cells) {
    await prisma.cell.upsert({
      where: { id: cell.id },
      update: {
        name: cell.name,
        sectorId: cell.sector_id,
      },
      create: {
        id: cell.id,
        code: cell.id,
        name: cell.name,
        sectorId: cell.sector_id,
      },
    });
  }

  console.log('Upserting villages...');
  for (const village of villages) {
    await prisma.village.upsert({
      where: { id: village.id },
      update: {
        name: village.name,
        cellId: village.cell_id,
      },
      create: {
        id: village.id,
        code: village.id,
        name: village.name,
        cellId: village.cell_id,
      },
    });
  }

  console.log('✅ Seeding locations completed.');
};

const seedAdmin = async () => {
  const role = await prisma.role.upsert({
    where: { name: 'Admin' },
    update: {},
    create: {
      name: 'Admin',
      code: 'ADMIN-USER',
      privileges: [Privilege.USER_MANAGEMENT, Privilege.DATA_COLLECTION],
    },
  });

  const hash = await bcrypt.hash(process.env.ADMIN_PASSWORD, 12);

  const admin = await prisma.user.upsert({
    where: { email: process.env.ADMIN_EMAIL},
    update: {},
    create: {
      firstName: process.env.ADMIN_FIRST_NAME, 
      lastName: process.env.ADMIN_LAST_NAME,
      email: process.env.ADMIN_EMAIL,
      telephoneNumber: process.env.ADMIN_PHONE,
      roleId: role.id,
      account: {
        create: {
          accountVerified: true,
          accountVerifiedAt: new Date(),
          password: hash,
        },
      },
    },
    include: { account: true },
  });

  console.log('✅ Seeding admin completed.', admin);
}

async function main() {
  await seedLocations();
  await seedAdmin();
}

main()
  .catch((e) => {
    console.error('❌ Error seeding data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
