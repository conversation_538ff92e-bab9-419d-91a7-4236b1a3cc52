import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateRoleDto } from './create-role.dto';
import { Privilege } from '@prisma/client';

export class UpdateRoleDto extends PartialType(CreateRoleDto) {}

export class UpdateRoleResponseDto {
  @ApiProperty({
    description: 'Updated role information',
  })
  role: {
    id: string;
    name: string;
    code: string;
    privileges: Privilege[];
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Role updated successfully',
  })
  message: string;
}
