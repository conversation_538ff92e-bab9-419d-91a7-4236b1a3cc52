import { ApiProperty, PartialType } from '@nestjs/swagger';
import { 
  BaseFacilityWithNameDto, 
  BaseFacilityWithNameResponseDto, 
  BasePaginatedResponseDto,
  BaseCreateResponseDto,
  BaseUpdateResponseDto
} from './base-facility.dto';

export class CreateMarketDto extends BaseFacilityWithNameDto {}

export class UpdateMarketDto extends PartialType(CreateMarketDto) {}

export class MarketResponseDto extends BaseFacilityWithNameResponseDto {}

export class MarketsListResponseDto extends BasePaginatedResponseDto<MarketResponseDto> {
  @ApiProperty({
    description: 'List of markets',
    type: [MarketResponseDto],
  })
  declare data: MarketResponseDto[];
}

export class CreateMarketResponseDto extends BaseCreateResponseDto<MarketResponseDto> {
  @ApiProperty({
    description: 'Created market information',
  })
  declare facility: MarketResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'Market created successfully',
  })
  declare message: string;
}

export class UpdateMarketResponseDto extends BaseUpdateResponseDto<MarketResponseDto> {
  @ApiProperty({
    description: 'Updated market information',
  })
  declare facility: MarketResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'Market updated successfully',
  })
  declare message: string;
}
