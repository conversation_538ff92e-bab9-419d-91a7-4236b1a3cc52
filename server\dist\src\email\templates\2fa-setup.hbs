<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set up Two-Factor Authentication</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #f8fafc;
            margin: 0;
            padding: 20px 0;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e7eb;
        }

        .header {
            background: #2078FF;
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 16px;
            opacity: 0.95;
            font-weight: 400;
        }

        .content {
            padding: 40px;
        }

        .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 24px;
            line-height: 1.7;
        }

        .button-container {
            text-align: center;
            margin: 32px 0;
        }

        .button {
            display: inline-block;
            background: #2078FF;
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease;
        }

        .button:hover {
            background: #1a66d9;
            transform: translateY(-1px);
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .info-box {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 12px;
            padding: 20px 24px;
            margin: 24px 0;
        }

        .info-box p {
            margin: 0;
            color: #1e40af;
            font-size: 14px;
            line-height: 1.6;
        }

        .warning-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px 24px;
            margin: 24px 0;
        }

        .warning-box p {
            margin: 0;
            color: #92400e;
            font-size: 14px;
            line-height: 1.6;
        }

        .code-block {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            word-break: break-all;
            font-size: 14px;
            margin: 24px 0;
            border: 1px solid #e2e8f0;
            box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .footer {
            background: #f8fafc;
            padding: 32px 40px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .footer p {
            margin: 8px 0;
            font-size: 14px;
            color: #6b7280;
        }

        .divider {
            height: 1px;
            background: #e5e7eb;
            margin: 24px 0;
        }

        @media only screen and (max-width: 600px) {
            body {
                padding: 10px 0;
            }

            .email-container {
                margin: 0 10px;
                border-radius: 12px;
            }

            .header, .content, .footer {
                padding: 24px 20px;
            }

            .header h1 {
                font-size: 24px;
            }

            .button {
                display: block;
                width: 100%;
                text-align: center;
                padding: 14px 24px;
            }

            .info-box, .warning-box {
                padding: 16px 20px;
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="header-content">
                <h1>WASH MIS</h1>
                <p>Water, Sanitation, and Hygiene Management Information System</p>
                <p style="font-size: 14px; margin-top: 8px; opacity: 0.9;">Ministry of Infrastructure - Rwanda</p>
            </div>
        </div>

        <div class="content">
            <div class="greeting">
                Hello {{firstName}},
            </div>

            <div class="message">
                Great! You've successfully set your password. Now it's time to enhance your account security by setting up Two-Factor Authentication (2FA).
            </div>

            <div class="message">
                Two-Factor Authentication adds an extra layer of security to your account by requiring a second form of verification when you log in. This helps protect your account even if someone else knows your password.
            </div>

            <div class="button-container">
                <a href="{{setupUrl}}" class="button">Set Up 2FA Now</a>
            </div>

            <div class="info-box">
                <p><strong>What you'll need:</strong> A smartphone with an authenticator app installed (such as Google Authenticator, Microsoft Authenticator, or Authy).</p>
            </div>

            <div class="message">
                The setup process is quick and easy:
                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li style="margin-bottom: 8px;">Click the setup button above to access your 2FA configuration</li>
                    <li style="margin-bottom: 8px;">Scan the QR code with your authenticator app</li>
                    <li style="margin-bottom: 8px;">Enter the 6-digit code from your app to verify the setup</li>
                    <li style="margin-bottom: 8px;">Save your recovery codes in a secure location</li>
                </ol>
            </div>

            <div class="warning-box">
                <p><strong>Important:</strong> After setting up 2FA, you'll need to use your authenticator app every time you log in. Make sure to save your recovery codes in a safe place - they're your backup access method if you lose your phone.</p>
            </div>

            <div class="message">
                If you're unable to click the button above, you can copy and paste the following link into your browser:
            </div>

            <div class="code-block">
                {{setupUrl}}
            </div>

            <div class="message">
                <strong>Why is 2FA important?</strong>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li style="margin-bottom: 5px;">Protects against password theft</li>
                    <li style="margin-bottom: 5px;">Prevents unauthorized access to sensitive WASH infrastructure data</li>
                    <li style="margin-bottom: 5px;">Meets security compliance requirements</li>
                    <li style="margin-bottom: 5px;">Gives you peace of mind</li>
                </ul>
            </div>

            <div class="message">
                If you need help with the setup process or have any questions about 2FA, please contact our support team.
            </div>

            <div class="message">
                Best regards,<br>
                <strong>WASH MIS Team</strong><br>
                Ministry of Infrastructure, Rwanda
            </div>
        </div>

        <div class="footer">
            <p>This email was sent from the WASH MIS</p>
            <p>Water, Sanitation, and Hygiene Management Information System</p>
            <p style="font-weight: 600; color: #475569;">Ministry of Infrastructure, Republic of Rwanda</p>

            {{#if supportEmail}}
            <p>Need help? Contact us at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a></p>
            {{/if}}

            {{#if supportPhone}}
            <p>Phone: {{supportPhone}}</p>
            {{/if}}

            <div class="divider"></div>

            <p style="font-size: 12px; color: #94a3b8;">
                &copy; {{year}} Ministry of Infrastructure, Rwanda. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
