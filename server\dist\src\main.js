"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("./tracing");
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const logger_1 = require("./logger");
const metrics_1 = require("./metrics");
const api_1 = require("@opentelemetry/api");
const appLogger = (0, logger_1.createModuleLogger)('main');
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['log', 'error', 'warn'],
    });
    app.useLogger(logger_1.logger);
    app.use((req, res, next) => {
        const startTime = Date.now();
        const end = metrics_1.httpRequestDuration.startTimer();
        metrics_1.httpActiveConnections.inc();
        const span = api_1.trace.getActiveSpan();
        if (span) {
            span.setAttributes({
                'http.method': req.method,
                'http.url': req.url,
                'http.user_agent': req.headers['user-agent'] || '',
            });
        }
        res.on('finish', () => {
            const duration = (Date.now() - startTime) / 1000;
            const route = req.route?.path || req.url;
            const endpoint = req.url.split('?')[0];
            end({
                method: req.method,
                route: route,
                status_code: res.statusCode.toString(),
                endpoint: endpoint,
            });
            metrics_1.httpRequestTotal.inc({
                method: req.method,
                route: route,
                status_code: res.statusCode.toString(),
                endpoint: endpoint,
            });
            metrics_1.httpActiveConnections.dec();
            appLogger.info('HTTP Request', {
                method: req.method,
                url: req.url,
                status_code: res.statusCode,
                duration_ms: duration * 1000,
                user_agent: req.headers['user-agent'],
                ip: req.ip || req.connection.remoteAddress,
            });
            if (span) {
                span.setAttributes({
                    'http.status_code': res.statusCode,
                    'http.response_size': res.get('content-length') || 0,
                });
            }
        });
        next();
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
    }));
    app.enableCors({
        origin: process.env.FE_URL || "http://localhost:3000",
        methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
        preflightContinue: false,
        credentials: true,
        optionsSuccessStatus: 204
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('WASH MIS API')
        .setDescription('API for the Ministry of Infrastructure Water, Sanitation, and Hygiene Management Information System')
        .setVersion('1.0')
        .addServer('/api/v1')
        .addBearerAuth()
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api-docs', app, document);
    app.setGlobalPrefix('api/v1');
    setInterval(() => {
        const memUsage = process.memoryUsage();
        appLogger.debug('Memory usage', {
            heap_used: memUsage.heapUsed,
            heap_total: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss,
        });
    }, 30000);
    const port = process.env.PORT || 8080;
    await app.listen(port);
    appLogger.info('WASH MIS API started successfully', {
        port: port,
        environment: process.env.NODE_ENV || 'development',
        url: await app.getUrl(),
    });
    console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap().catch((error) => {
    appLogger.error('Failed to start application', { error: error.message, stack: error.stack });
    process.exit(1);
});
//# sourceMappingURL=main.js.map