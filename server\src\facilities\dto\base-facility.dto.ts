import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsInt, Min, IsOptional, IsNumber } from 'class-validator';

export class BaseFacilityDto {
  @ApiProperty({
    description: 'Location ID where the facility is located',
    example: 'location_123',
  })
  @IsString()
  locationId: string;

  @ApiProperty({
    description: 'Facility number (unique identifier)',
    example: 1001,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  number: number;
}

export class BaseFacilityWithNameDto extends BaseFacilityDto {
  @ApiProperty({
    description: 'Facility name',
    example: 'Central Health Center',
  })
  @IsString()
  name: string;
}

export class PaginationQueryDto {
  @ApiProperty({
    description: 'Page number',
    example: 1,
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({
    description: 'Search term for filtering facilities',
    example: 'health center',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}

export class BaseFacilityResponseDto {
  @ApiProperty({
    description: 'Facility ID',
    example: 'facility_123',
  })
  id: string;

  @ApiProperty({
    description: 'Facility number',
    example: 1001,
  })
  number: number;

  @ApiProperty({
    description: 'Location information',
  })
  location: {
    id: string;
    villageId: number;
    village: {
      id: number;
      name: string;
      cell: {
        id: number;
        name: string;
        sector: {
          id: number;
          name: string;
          district: {
            id: number;
            name: string;
            province: {
              id: number;
              name: string;
            };
          };
        };
      };
    };
    latitude?: number;
    longitude?: number;
    settlementType: string;
  };

  @ApiProperty({
    description: 'Facility creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Facility last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class BaseFacilityWithNameResponseDto extends BaseFacilityResponseDto {
  @ApiProperty({
    description: 'Facility name',
    example: 'Central Health Center',
  })
  name: string;
}

export class BasePaginatedResponseDto<T> {
  @ApiProperty({
    description: 'Total number of items',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages: number;

  data: T[];
}

export class BaseCreateResponseDto<T> {
  @ApiProperty({
    description: 'Success message',
    example: 'Facility created successfully',
  })
  message: string;

  facility: T;
}

export class BaseUpdateResponseDto<T> {
  @ApiProperty({
    description: 'Success message',
    example: 'Facility updated successfully',
  })
  message: string;

  facility: T;
}

export class BaseDeleteResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Facility deleted successfully',
  })
  message: string;
}
