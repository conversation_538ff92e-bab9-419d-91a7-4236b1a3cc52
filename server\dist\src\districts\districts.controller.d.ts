import { DistrictsService } from './districts.service';
import { CreateDistrictDto, CreateDistrictResponseDto } from './dto/create-district.dto';
import { UpdateDistrictDto, UpdateDistrictResponseDto } from './dto/update-district.dto';
import { DistrictResponseDto, DistrictsListResponseDto } from './dto/district-response.dto';
export declare class DistrictsController {
    private readonly districtsService;
    constructor(districtsService: DistrictsService);
    create(createDistrictDto: CreateDistrictDto): Promise<CreateDistrictResponseDto>;
    findAll(page?: number, limit?: number, search?: string, provinceId?: number): Promise<DistrictsListResponseDto>;
    findOne(id: number): Promise<DistrictResponseDto>;
    update(id: number, updateDistrictDto: UpdateDistrictDto): Promise<UpdateDistrictResponseDto>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
