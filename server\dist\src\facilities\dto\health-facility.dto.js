"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateHealthFacilityResponseDto = exports.CreateHealthFacilityResponseDto = exports.HealthFacilitiesListResponseDto = exports.HealthFacilityResponseDto = exports.UpdateHealthFacilityDto = exports.CreateHealthFacilityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const base_facility_dto_1 = require("./base-facility.dto");
class CreateHealthFacilityDto extends base_facility_dto_1.BaseFacilityWithNameDto {
}
exports.CreateHealthFacilityDto = CreateHealthFacilityDto;
class UpdateHealthFacilityDto extends (0, swagger_1.PartialType)(CreateHealthFacilityDto) {
}
exports.UpdateHealthFacilityDto = UpdateHealthFacilityDto;
class HealthFacilityResponseDto extends base_facility_dto_1.BaseFacilityWithNameResponseDto {
}
exports.HealthFacilityResponseDto = HealthFacilityResponseDto;
class HealthFacilitiesListResponseDto extends base_facility_dto_1.BasePaginatedResponseDto {
    data;
}
exports.HealthFacilitiesListResponseDto = HealthFacilitiesListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of health facilities',
        type: [HealthFacilityResponseDto],
    }),
    __metadata("design:type", Array)
], HealthFacilitiesListResponseDto.prototype, "data", void 0);
class CreateHealthFacilityResponseDto extends base_facility_dto_1.BaseCreateResponseDto {
    facility;
    message;
}
exports.CreateHealthFacilityResponseDto = CreateHealthFacilityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created health facility information',
    }),
    __metadata("design:type", HealthFacilityResponseDto)
], CreateHealthFacilityResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Health facility created successfully',
    }),
    __metadata("design:type", String)
], CreateHealthFacilityResponseDto.prototype, "message", void 0);
class UpdateHealthFacilityResponseDto extends base_facility_dto_1.BaseUpdateResponseDto {
    facility;
    message;
}
exports.UpdateHealthFacilityResponseDto = UpdateHealthFacilityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated health facility information',
    }),
    __metadata("design:type", HealthFacilityResponseDto)
], UpdateHealthFacilityResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Health facility updated successfully',
    }),
    __metadata("design:type", String)
], UpdateHealthFacilityResponseDto.prototype, "message", void 0);
//# sourceMappingURL=health-facility.dto.js.map