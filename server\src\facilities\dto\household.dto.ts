import { ApiProperty, PartialType } from '@nestjs/swagger';
import { 
  BaseFacilityDto, 
  BaseFacilityResponseDto, 
  BasePaginatedResponseDto,
  BaseCreateResponseDto,
  BaseUpdateResponseDto
} from './base-facility.dto';

export class CreateHouseholdDto extends BaseFacilityDto {}

export class UpdateHouseholdDto extends PartialType(CreateHouseholdDto) {}

export class HouseholdResponseDto extends BaseFacilityResponseDto {}

export class HouseholdsListResponseDto extends BasePaginatedResponseDto<HouseholdResponseDto> {
  @ApiProperty({
    description: 'List of households',
    type: [HouseholdResponseDto],
  })
  declare data: HouseholdResponseDto[];
}

export class CreateHouseholdResponseDto extends BaseCreateResponseDto<HouseholdResponseDto> {
  @ApiProperty({
    description: 'Created household information',
  })
  declare facility: HouseholdResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'Household created successfully',
  })
  declare message: string;
}

export class UpdateHouseholdResponseDto extends BaseUpdateResponseDto<HouseholdResponseDto> {
  @ApiProperty({
    description: 'Updated household information',
  })
  declare facility: HouseholdResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'Household updated successfully',
  })
  declare message: string;
}
