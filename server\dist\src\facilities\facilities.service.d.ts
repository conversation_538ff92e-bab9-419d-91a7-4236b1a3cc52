import { PrismaService } from '../prisma/prisma.service';
import { CreateHouseholdDto, UpdateHouseholdDto, HouseholdResponseDto, HouseholdsListResponseDto, CreateHouseholdResponseDto, UpdateHouseholdResponseDto } from './dto/household.dto';
import { CreateSchoolDto, UpdateSchoolDto, SchoolResponseDto, SchoolsListResponseDto, CreateSchoolResponseDto, UpdateSchoolResponseDto } from './dto/school.dto';
import { CreateHealthFacilityDto, UpdateHealthFacilityDto, HealthFacilityResponseDto, HealthFacilitiesListResponseDto, CreateHealthFacilityResponseDto, UpdateHealthFacilityResponseDto } from './dto/health-facility.dto';
import { CreateMarketDto, UpdateMarketDto, MarketResponseDto, MarketsListResponseDto, CreateMarketResponseDto, UpdateMarketResponseDto } from './dto/market.dto';
import { BaseDeleteResponseDto } from './dto/base-facility.dto';
export declare class FacilitiesService {
    private prisma;
    constructor(prisma: PrismaService);
    createHousehold(createHouseholdDto: CreateHouseholdDto): Promise<CreateHouseholdResponseDto>;
    findAllHouseholds(page?: number, limit?: number, search?: string): Promise<HouseholdsListResponseDto>;
    findOneHousehold(id: string): Promise<HouseholdResponseDto>;
    updateHousehold(id: string, updateHouseholdDto: UpdateHouseholdDto): Promise<UpdateHouseholdResponseDto>;
    removeHousehold(id: string): Promise<BaseDeleteResponseDto>;
    private formatHouseholdResponse;
    createSchool(createSchoolDto: CreateSchoolDto): Promise<CreateSchoolResponseDto>;
    findAllSchools(page?: number, limit?: number, search?: string): Promise<SchoolsListResponseDto>;
    findOneSchool(id: string): Promise<SchoolResponseDto>;
    updateSchool(id: string, updateSchoolDto: UpdateSchoolDto): Promise<UpdateSchoolResponseDto>;
    removeSchool(id: string): Promise<BaseDeleteResponseDto>;
    private formatSchoolResponse;
    createHealthFacility(createHealthFacilityDto: CreateHealthFacilityDto): Promise<CreateHealthFacilityResponseDto>;
    findAllHealthFacilities(page?: number, limit?: number, search?: string): Promise<HealthFacilitiesListResponseDto>;
    findOneHealthFacility(id: string): Promise<HealthFacilityResponseDto>;
    updateHealthFacility(id: string, updateHealthFacilityDto: UpdateHealthFacilityDto): Promise<UpdateHealthFacilityResponseDto>;
    removeHealthFacility(id: string): Promise<BaseDeleteResponseDto>;
    private formatHealthFacilityResponse;
    createMarket(createMarketDto: CreateMarketDto): Promise<CreateMarketResponseDto>;
    findAllMarkets(page?: number, limit?: number, search?: string): Promise<MarketsListResponseDto>;
    findOneMarket(id: string): Promise<MarketResponseDto>;
    updateMarket(id: string, updateMarketDto: UpdateMarketDto): Promise<UpdateMarketResponseDto>;
    removeMarket(id: string): Promise<BaseDeleteResponseDto>;
    private formatMarketResponse;
}
