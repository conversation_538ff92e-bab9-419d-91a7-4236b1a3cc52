import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { DistrictsService } from './districts.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { PrivilegeGuard } from '../common/guards/privilege.guard';
import { MobileGuard } from '../common/guards/mobile.guard';
import { Privileges } from '../common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';
import {
  CreateDistrictDto,
  CreateDistrictResponseDto,
} from './dto/create-district.dto';
import {
  UpdateDistrictDto,
  UpdateDistrictResponseDto,
} from './dto/update-district.dto';
import {
  DistrictResponseDto,
  DistrictsListResponseDto,
} from './dto/district-response.dto';

@ApiTags('Districts')
@Controller('districts')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
export class DistrictsController {
  constructor(private readonly districtsService: DistrictsService) {}

  @Post()
  @Privileges(Privilege.USER_MANAGEMENT)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new district' })
  @ApiResponse({
    status: 201,
    description: 'District created successfully',
    type: CreateDistrictResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Province not found' })
  @ApiResponse({ status: 409, description: 'District already exists' })
  async create(@Body() createDistrictDto: CreateDistrictDto) {
    return this.districtsService.create(createDistrictDto);
  }

  @Get()
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get all districts with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by district name or code',
    example: 'Nyarugenge',
  })
  @ApiQuery({
    name: 'provinceId',
    required: false,
    type: Number,
    description: 'Filter by province ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Districts retrieved successfully',
    type: DistrictsListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('search') search?: string,
    @Query('provinceId', new ParseIntPipe({ optional: true })) provinceId?: number,
  ) {
    return this.districtsService.findAll(page, limit, search, provinceId);
  }

  @Get(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get a district by ID' })
  @ApiParam({
    name: 'id',
    description: 'District ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'District retrieved successfully',
    type: DistrictResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'District not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.districtsService.findOne(id);
  }

  @Patch(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Update a district' })
  @ApiParam({
    name: 'id',
    description: 'District ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'District updated successfully',
    type: UpdateDistrictResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'District or Province not found' })
  @ApiResponse({ status: 409, description: 'District already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateDistrictDto: UpdateDistrictDto) {
    return this.districtsService.update(id, updateDistrictDto);
  }

  @Delete(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Delete a district' })
  @ApiParam({
    name: 'id',
    description: 'District ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'District deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'District deleted successfully',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - District has sectors' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'District not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.districtsService.remove(id);
  }
}
