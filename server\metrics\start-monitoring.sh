#!/bin/bash

# WASH MIS Monitoring Stack Startup Script

set -e

echo "🚀 Starting WASH MIS LGTM Monitoring Stack..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p data/{mimir,loki,tempo,prometheus,grafana}

# Set proper permissions
echo "🔐 Setting permissions..."
chmod -R 755 data/
chmod -R 755 logs/

# Pull latest images
echo "📥 Pulling latest Docker images..."
docker-compose pull

# Start the monitoring stack
echo "🏗️  Starting monitoring services..."
docker-compose up -d

# Check for any immediate startup errors
echo "🔍 Checking for startup errors..."
sleep 5

# Check if any containers failed to start
failed_containers=$(docker-compose ps --services --filter "status=exited")
if [ ! -z "$failed_containers" ]; then
    echo "❌ Some containers failed to start:"
    echo "$failed_containers"
    echo ""
    echo "📋 Container logs:"
    for container in $failed_containers; do
        echo "--- $container logs ---"
        docker-compose logs --tail=20 $container
        echo ""
    done
    echo "💡 Check CONFIGURATION_FIXES.md for troubleshooting steps"
    exit 1
fi

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."

# Function to check if a service is ready
check_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1

    echo "   Checking $service_name..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo "   ✅ $service_name is ready"
            return 0
        fi
        
        echo "   ⏳ Waiting for $service_name (attempt $attempt/$max_attempts)..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "   ❌ $service_name failed to start within expected time"
    return 1
}

# Check each service
check_service "Mimir" "http://localhost:9009/ready"
check_service "Prometheus" "http://localhost:9090/-/ready"
check_service "Loki" "http://localhost:3100/ready"
check_service "Tempo" "http://localhost:3200/ready"
check_service "Grafana" "http://localhost:3001/api/health"

echo ""
echo "🎉 WASH MIS Monitoring Stack is ready!"
echo ""
echo "📊 Access URLs:"
echo "   Grafana:    http://localhost:3001 (admin/admin)"
echo "   Prometheus: http://localhost:9090"
echo "   Mimir:      http://localhost:9009"
echo "   Loki:       http://localhost:3100"
echo "   Tempo:      http://localhost:3200"
echo ""
echo "📈 Pre-configured Dashboards:"
echo "   - WASH MIS Overview"
echo "   - WASH MIS Business Metrics"
echo "   - NestJS Application Performance"
echo "   - LGTM Stack Infrastructure"
echo ""
echo "🔔 Alerting Rules:"
echo "   - Application performance alerts"
echo "   - Business metric alerts"
echo "   - Infrastructure health alerts"
echo ""
echo "💡 Next Steps:"
echo "   1. Start your WASH MIS application: cd .. && pnpm run dev"
echo "   2. Generate some traffic to see metrics"
echo "   3. Check the dashboards in Grafana"
echo "   4. Review and customize alert rules as needed"
echo ""
echo "📚 For more information, see metrics/README.md"
echo ""
echo "🛑 To stop the monitoring stack: docker-compose down"
echo "🗑️  To remove all data: docker-compose down -v"
