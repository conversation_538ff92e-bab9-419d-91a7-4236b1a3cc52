import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateCellDto,
  CreateCellResponseDto,
} from './dto/create-cell.dto';
import {
  UpdateCellDto,
  UpdateCellResponseDto,
} from './dto/update-cell.dto';
import {
  CellResponseDto,
  CellsListResponseDto,
} from './dto/cell-response.dto';

@Injectable()
export class CellsService {
  constructor(private prisma: PrismaService) {}

  async create(createCellDto: CreateCellDto): Promise<CreateCellResponseDto> {
    const { code, name, sectorId } = createCellDto;

    // Check if sector exists
    const sector = await this.prisma.sector.findUnique({
      where: { id: sectorId },
    });

    if (!sector) {
      throw new NotFoundException('Sector not found');
    }

    // Check if cell with same code or name already exists
    const existingCell = await this.prisma.cell.findFirst({
      where: {
        OR: [
          { code },
          { name },
        ],
      },
    });

    if (existingCell) {
      if (existingCell.code === code) {
        throw new ConflictException('Cell with this code already exists');
      }
      if (existingCell.name === name) {
        throw new ConflictException('Cell with this name already exists');
      }
    }

    // Create cell
    const cell = await this.prisma.cell.create({
      data: {
        code,
        name,
        sectorId,
      },
    });

    return {
      cell: {
        id: cell.id,
        code: cell.code,
        name: cell.name,
        sectorId: cell.sectorId,
        createdAt: cell.createdAt,
      },
      message: 'Cell created successfully',
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sectorId?: number,
  ): Promise<CellsListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      AND: [
        search
          ? {
              OR: [
                { name: { contains: search, mode: 'insensitive' as const } },
                { code: { equals: isNaN(Number(search)) ? undefined : Number(search) } },
              ].filter(Boolean),
            }
          : {},
        sectorId ? { sectorId } : {},
      ],
    };

    const [cells, total] = await Promise.all([
      this.prisma.cell.findMany({
        where,
        skip,
        take: limit,
        include: {
          sector: {
            include: {
              district: {
                include: {
                  province: true,
                },
              },
            },
          },
          _count: {
            select: { villages: true },
          },
        },
        orderBy: {
          code: 'asc',
        },
      }),
      this.prisma.cell.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      cells: cells.map((cell) => ({
        id: cell.id,
        code: cell.code,
        name: cell.name,
        sector: {
          id: cell.sector.id,
          name: cell.sector.name,
          code: cell.sector.code,
          district: {
            id: cell.sector.district.id,
            name: cell.sector.district.name,
            code: cell.sector.district.code,
            province: {
              id: cell.sector.district.province.id,
              name: cell.sector.district.province.name,
              code: cell.sector.district.province.code,
            },
          },
        },
        villageCount: cell._count.villages,
        createdAt: cell.createdAt,
        updatedAt: cell.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: number): Promise<CellResponseDto> {
    const cell = await this.prisma.cell.findUnique({
      where: { id },
      include: {
        sector: {
          include: {
            district: {
              include: {
                province: true,
              },
            },
          },
        },
        _count: {
          select: { villages: true },
        },
      },
    });

    if (!cell) {
      throw new NotFoundException('Cell not found');
    }

    return {
      id: cell.id,
      code: cell.code,
      name: cell.name,
      sector: {
        id: cell.sector.id,
        name: cell.sector.name,
        code: cell.sector.code,
        district: {
          id: cell.sector.district.id,
          name: cell.sector.district.name,
          code: cell.sector.district.code,
          province: {
            id: cell.sector.district.province.id,
            name: cell.sector.district.province.name,
            code: cell.sector.district.province.code,
          },
        },
      },
      villageCount: cell._count.villages,
      createdAt: cell.createdAt,
      updatedAt: cell.updatedAt,
    };
  }

  async update(id: number, updateCellDto: UpdateCellDto): Promise<UpdateCellResponseDto> {
    const { code, name, sectorId } = updateCellDto;

    // Check if cell exists
    const existingCell = await this.prisma.cell.findUnique({
      where: { id },
    });

    if (!existingCell) {
      throw new NotFoundException('Cell not found');
    }

    // Check if sector exists if provided
    if (sectorId) {
      const sector = await this.prisma.sector.findUnique({
        where: { id: sectorId },
      });

      if (!sector) {
        throw new NotFoundException('Sector not found');
      }
    }

    // Check for conflicts with other cells
    if (code || name) {
      const conflictCell = await this.prisma.cell.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                code ? { code } : {},
                name ? { name } : {},
              ].filter(Boolean),
            },
          ],
        },
      });

      if (conflictCell) {
        if (conflictCell.code === code) {
          throw new ConflictException('Cell with this code already exists');
        }
        if (conflictCell.name === name) {
          throw new ConflictException('Cell with this name already exists');
        }
      }
    }

    // Update cell
    const updatedCell = await this.prisma.cell.update({
      where: { id },
      data: {
        ...(code && { code }),
        ...(name && { name }),
        ...(sectorId && { sectorId }),
      },
    });

    return {
      cell: {
        id: updatedCell.id,
        code: updatedCell.code,
        name: updatedCell.name,
        sectorId: updatedCell.sectorId,
        updatedAt: updatedCell.updatedAt,
      },
      message: 'Cell updated successfully',
    };
  }

  async remove(id: number): Promise<{ message: string }> {
    // Check if cell exists
    const existingCell = await this.prisma.cell.findUnique({
      where: { id },
      include: {
        _count: {
          select: { villages: true },
        },
      },
    });

    if (!existingCell) {
      throw new NotFoundException('Cell not found');
    }

    // Check if cell has villages
    if (existingCell._count.villages > 0) {
      throw new BadRequestException(
        `Cannot delete cell. ${existingCell._count.villages} village(s) belong to this cell`,
      );
    }

    // Delete cell
    await this.prisma.cell.delete({
      where: { id },
    });

    return { message: 'Cell deleted successfully' };
  }
}
