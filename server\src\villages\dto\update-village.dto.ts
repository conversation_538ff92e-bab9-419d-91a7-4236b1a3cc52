import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateVillageDto } from './create-village.dto';

export class UpdateVillageDto extends PartialType(CreateVillageDto) {}

export class UpdateVillageResponseDto {
  @ApiProperty({
    description: 'Updated village information',
  })
  village: {
    id: number;
    code: number;
    name: string;
    cellId: number;
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Village updated successfully',
  })
  message: string;
}
